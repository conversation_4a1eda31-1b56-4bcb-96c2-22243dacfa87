#pragma once
#include <algorithm>
#include <iomanip>
#include <map>
#include <memory>
#include <mutex>
#include <sstream>
#include <string>
#include <vector>

class DatabaseManager;
class User;

class UserManager {
    private:
        std::vector<std::shared_ptr<User>> users;
        std::shared_ptr<DatabaseManager>   dbManager;
        std::shared_ptr<User>              currentUser;
        int                                maxLoginAttempts = 3;  // 最大登录尝试次数
        std::map<std::string, int>         loginAttempts;         // 用户名到登录尝试次数的映射
    public:
        // 构造函数
        explicit UserManager(std::shared_ptr<DatabaseManager> dbManager);

        // 用户登录
        bool login(const std::string& username, const std::string& password);

        // 用户登出
        void logout();

        // 注册新用户
        bool registerUser(const std::string& username, const std::string& password, const std::string& userType = "regular");

        // 删除用户
        bool deleteUser(const std::string& username);

        // 修改密码
        bool changePassword(const std::string& username, const std::string& newPassword);

        // 获取所有用户
        const std::vector<std::shared_ptr<User>>& getAllUsers();

        // 根据用户名查找用户
        std::shared_ptr<User> findUserByUsername(const std::string& username) const;

        // 获取当前用户
        std::shared_ptr<User> getCurrentUser() const;

        // 检查用户是否登录
        bool isLoggedIn() const;

        // 哈希密码
        std::string hashPassword(const std::string& password);

        // 验证密码
        bool verifyPassword(int userId, const std::string& password);

        // 检查用户名是否已存在
        bool isUsernameExists(const std::string& username) const;

        // 读取隐藏密码输入
        std::string getHiddenPassword(const std::string& prompt);

        // 设置最大登录尝试次数
        void setMaxLoginAttempts(int attempts);

        // 检查用户是否被临时阻止登录
        bool isUserTemporarilyBlocked(const std::string& username);

        // 重置用户登录尝试次数
        void resetLoginAttempts(const std::string& username);
};
