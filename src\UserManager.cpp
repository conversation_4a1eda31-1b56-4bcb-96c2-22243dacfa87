#include "../include/UserManager.h"
#include <conio.h>
#include "../include/DatabaseManager.h"
#include "../include/Logger.h"
#include "../include/User.h"

using namespace std;

UserManager::UserManager(std::shared_ptr<DatabaseManager> dbManager) : dbManager(dbManager) {
    auto logger = Logger::getInstance();
    logger->info("用户管理系统初始化完成");
    // 启动时加载所有用户到 users 容器
    users = dbManager->loadAllUsers();
}

// 用户登录（纯功能）
bool UserManager::login(const std::string& username, const std::string& password) {
    shared_ptr<User> user = dbManager->loadUser(username);
    if (user && dbManager->validateCredentials(username, hashPassword(password))) {
        currentUser = user;
        currentUser->setLoginStatus(true);

        // 检查 users 容器中是否已存在该用户
        auto it = std::find_if(users.begin(), users.end(), [&user](const std::shared_ptr<User>& u) {
            return u->getUserId() == user->getUserId();
        });
        if (it == users.end()) {
            users.push_back(user);
        }

        return true;  // 登录成功
    }

    return false;  // 登录失败
}

// 用户登出（纯功能）
void UserManager::logout() {
    if (currentUser) {
        currentUser->setLoginStatus(false);
        currentUser.reset();
    }
}

// 注册新用户（纯功能）
bool UserManager::registerUser(const std::string& username, const std::string& password, const std::string& userType) {
    if (isUsernameExists(username)) {
        return false;  // 用户名已存在
    }
    // 使用占位符ID（-1表示待从数据库获取）
    shared_ptr<User> newUser = UserFactory::createUser(-1, username, hashPassword(password), userType);

    // 保存用户到数据库
    if (!dbManager->saveUser(newUser)) {
        return false;  // 保存失败
    }

    // 从数据库重新加载用户以获取正确的ID
    shared_ptr<User> savedUser = dbManager->loadUser(username);
    if (savedUser) {
        users.push_back(savedUser);
        return true;  // 注册成功
    } else {
        return false;  // 注册后加载失败
    }
}

// 删除用户
bool UserManager::deleteUser(const string& username) {
    auto logger = Logger::getInstance();

    auto it = std::find_if(users.begin(), users.end(), [&username](const std::shared_ptr<User>& user) { return user->getUsername() == username; });

    if (it == users.end()) {
        logger->warning("删除用户失败：用户名为 " + username + " 的用户不存在", "System");
        return false;
    }

    std::string Currentusername = (*it)->getUsername();

    // 检查是否是当前登录用户
    if (currentUser && currentUser->getUsername() == username) {
        logger->info("用户 '" + username + "' 删除自己的账户并登出系统", username);
        logout();
    }

    int userId = (*it)->getUserId();
    // 从数据库删除
    if (!dbManager->deleteUser(userId)) {
        logger->mistake("【错误】从数据库删除用户 '" + username + "' 失败", "System");
        std::cout << "【操作失败】从数据库删除用户失败，请稍后重试。" << std::endl;
        return false;
    }

    // 从本地缓存删除
    users.erase(it);
    logger->info("用户 '" + username + "' (ID: " + std::to_string(userId) + ") 已被成功删除", "System");
    return true;
}

// 修改密码
bool UserManager::changePassword(const string& username, const std::string& newPassword) {
    auto logger = Logger::getInstance();

    // 查找用户
    auto it = std::find_if(users.begin(), users.end(), [&username](const std::shared_ptr<User>& user) { return user->getUsername() == username; });

    if (it == users.end()) {
        logger->warning("修改密码失败：用户名为 " + username + " 的用户不存在", "System");
        return false;  // 用户未找到
    }

    int    currentUserid   = (*it)->getUserId();
    string currentUsertype = (*it)->getUserType();

    // 记录详细日志以便调试
    logger->info("修改密码过程 - 原始用户ID: " + to_string(currentUserid) +
                 ", 用户名: " + username + ", 用户类型: " + currentUsertype);
    // 更新用户密码
    (*it)->setLoginStatus(false);
    string newPasswordHash = hashPassword(newPassword);

    // 直接修改现有用户对象的密码哈希
    (*it)->setPasswordHash(newPasswordHash);

    // 保存更新到数据库
    if (!dbManager->saveUser(*it)) {
        logger->mistake("修改密码失败：无法保存用户 '" + username + "' 的新密码到数据库", "System");
        return false;
    }

    if (currentUser && currentUser->getUserId() == currentUserid) {
        logger->info("您的密码已成功修改", username);
    } else {
        logger->info("用户 '" + username + "' 的密码已被管理员修改", "System");
    }

    return true;
}

// 获取所有用户
const std::vector<std::shared_ptr<User>>& UserManager::getAllUsers() {
    auto logger = Logger::getInstance();

    // 如果users为空，从数据库加载
    if (users.empty()) {
        logger->info("从数据库加载所有用户");
        users = dbManager->loadAllUsers();
    }

    return users;
}

// 根据用户名查找用户
std::shared_ptr<User> UserManager::findUserByUsername(const std::string& username) const {
    auto it = std::find_if(users.begin(), users.end(), [&username](const std::shared_ptr<User>& user) {
        return user->getUsername() == username;
    });

    if (it == users.end()) {
        return nullptr;
    }

    return *it;
}

// 获取当前用户
std::shared_ptr<User> UserManager::getCurrentUser() const {
    return currentUser;
}

// 检查用户是否登录
bool UserManager::isLoggedIn() const {
    return currentUser != nullptr && currentUser->getLoginStatus();
}

// 哈希密码
std::string UserManager::hashPassword(const std::string& password) {
    // 使用简单的哈希算法示例（实际应使用更安全的方法，如bcrypt）
    std::stringstream ss;
    ss << std::hex << std::uppercase;
    for (unsigned char c : password) {
        ss << std::setw(2) << std::setfill('0') << static_cast<int>(c);
    }
    return ss.str();
}

// 验证密码
bool UserManager::verifyPassword(int userId, const std::string& password) {
    auto logger = Logger::getInstance();

    auto it = std::find_if(users.begin(), users.end(), [userId](const std::shared_ptr<User>& user) {
        return user->getUserId() == userId;
    });

    if (it == users.end()) {
        logger->warning("密码验证失败：找不到ID为 " + std::to_string(userId) + " 的用户", "System");
        return false;  // 用户未找到
    }

    bool result = (*it)->getPasswordHash() == hashPassword(password);
    if (!result) {
        logger->warning("密码验证失败：用户 '" + (*it)->getUsername() + "' 输入了错误的密码", "System");
    }
    return result;
}

// 检查用户名是否已存在
bool UserManager::isUsernameExists(const std::string& username) const {
    for (const auto& user : users) {
        if (user->getUsername() == username) {
            return true;
        }
    }
    return false;
}

// 设置最大登录尝试次数（纯功能）
bool UserManager::setMaxLoginAttempts(int attempts) {
    if (attempts <= 0) {
        return false;  // 无效的尝试次数
    }

    maxLoginAttempts = attempts;
    return true;  // 设置成功
}

// 检查用户是否被临时阻止登录
bool UserManager::isUserTemporarilyBlocked(const std::string& username) {
    auto it = loginAttempts.find(username);
    if (it != loginAttempts.end()) {
        return it->second >= maxLoginAttempts;
    }
    return false;
}

// 重置用户登录尝试次数（纯功能）
void UserManager::resetLoginAttempts(const std::string& username) {
    auto it = loginAttempts.find(username);
    if (it != loginAttempts.end()) {
        loginAttempts.erase(it);
    }
}