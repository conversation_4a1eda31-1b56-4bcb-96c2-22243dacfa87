#pragma once

#include <functional>
#include <iostream>
#include <map>
#include <memory>
#include <string>
#include <vector>

// 前向声明
class UserManager;
class ContactManager;
class Logger;
class User;

// 菜单项类
class MenuItem {
private:
    std::string name;
    std::string description;
    std::function<void()> action;

public:
    MenuItem(const std::string& name, const std::string& description, std::function<void()> action);

    std::string getName() const;
    std::string getDescription() const;
    void execute() const;
};

// 菜单类
class Menu {
private:
    std::string title;
    std::vector<MenuItem> items;
    bool exitOption;

public:
    explicit Menu(const std::string& title, bool exitOption = true);

    void addItem(const MenuItem& item);
    void display() const;
    bool handleChoice();
};

// 菜单系统类
class MenuSystem {
private:
    std::shared_ptr<UserManager> userManager;
    std::shared_ptr<ContactManager> contactManager;
    std::shared_ptr<Logger> logger;

    // 各种菜单
    std::unique_ptr<Menu> mainMenu;
    std::unique_ptr<Menu> loginMenu;
    std::unique_ptr<Menu> contactMenu;
    std::unique_ptr<Menu> adminMenu;
    std::unique_ptr<Menu> userMenu;

    // 当前活动菜单
    Menu* currentMenu;

    // 私有方法用于初始化各种菜单
    void initMainMenu();
    void initLoginMenu();
    void initContactMenu();
    void initAdminMenu();
    void initUserMenu();

    // 菜单操作方法
    void login();
    void registerUser();
    void logout();
    void viewContacts();
    void addContact();
    void deleteContact();
    void updateContact();
    void searchContact();
    void adminManageUsers();
    void adminViewLogs();
    void changePassword();
    void exportContacts();
    void importContacts();
    void showHelp();
    void exit();

    // 辅助方法
    void clearScreen() const;
    void pauseScreen() const;
    std::string getPasswordInput(const std::string& prompt) const;
    void displayHeader(const std::string& title) const;
    void displayFooter() const;
    void showStartupAnimation(); // 显示欢迎界面
    void displayContactTableHeader() const; // 显示美化的联系人列表标题

public:
    MenuSystem(std::shared_ptr<UserManager> userManager, 
               std::shared_ptr<ContactManager> contactManager,
               std::shared_ptr<Logger> logger);
    ~MenuSystem();

    // 启动菜单系统
    void run();

    // 切换菜单
    void switchToMenu(const std::string& menuName);

    // 返回上一级菜单
    void goBack();
};
