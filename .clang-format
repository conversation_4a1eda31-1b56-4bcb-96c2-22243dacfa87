# 语言: C, C++
Language: Cpp

# 基于的编码规范,基础代码风格（可选：LLVM, Google, Chromium, Mozilla, WebKit）
BasedOnStyle: Google

# 访问说明符(public、private等)的偏移
AccessModifierOffset: 4  # 设置 public 等访问修饰符后退 4 格

IndentAccessModifiers: true

# 开括号(开圆括号、开尖括号、开方括号)后的对齐
AlignAfterOpenBracket: Align

# 连续赋值时，对齐所有等号 - 优化：启用对齐以提高可读性
AlignConsecutiveAssignments: true

# 连续声明时，对齐所有声明的变量名 - 优化：启用对齐以提高可读性
AlignConsecutiveDeclarations: true

# 连续宏定义的对齐 - 优化：新增选项
AlignConsecutiveMacros: true

# 左对齐逃脱换行(使用反斜杠换行)的反斜杠
AlignEscapedNewlines: Left

# 水平对齐二元和三元表达式的操作数
AlignOperands: true

# 对齐连续的尾随的注释
AlignTrailingComments: true

# 允许函数声明的所有参数在放在下一行
AllowAllParametersOfDeclarationOnNextLine: true

# 允许短的块放在同一行 - 优化：对于简短块允许单行
AllowShortBlocksOnASingleLine: true

# 允许短的case标签放在同一行
AllowShortCaseLabelsOnASingleLine: true

# 允许短的函数放在同一行
AllowShortFunctionsOnASingleLine: Inline

# 允许短的if语句保持在同一行 - 优化：对于简单条件允许单行
AllowShortIfStatementsOnASingleLine: false

# 允许短的循环保持在同一行 - 优化：对于简单循环允许单行
AllowShortLoopsOnASingleLine: false

# 允许短的lambda放在同一行 - 优化：新增选项
AllowShortLambdasOnASingleLine: All

# 总是在定义返回类型后换行
AlwaysBreakAfterDefinitionReturnType: None

# 总是在返回类型后换行
AlwaysBreakAfterReturnType: None

# 总是在多行string字面量前换行
AlwaysBreakBeforeMultilineStrings: true

# 总是在template声明后换行
AlwaysBreakTemplateDeclarations: Yes

# false表示函数实参要么都在同一行，要么都各自一行
BinPackArguments: true

# false表示所有形参要么都在同一行，要么都各自一行
BinPackParameters: true

# 位域冒号对齐方式 - 优化：新增选项
BitFieldColonSpacing: Both

# 大括号换行，只有当BreakBeforeBraces设置为Custom时才有效
BraceWrapping:
  # class定义后面
  AfterClass: false
  # 控制语句后面
  AfterControlStatement: false
  # enum定义后面
  AfterEnum: false
  # 函数定义后面
  AfterFunction: false
  # 命名空间定义后面
  AfterNamespace: false
  # ObjC定义后面
  AfterObjCDeclaration: false
  # struct定义后面
  AfterStruct: false
  # union定义后面
  AfterUnion: false
  # catch之前
  BeforeCatch: false
  # else之前
  BeforeElse: false
  # 缩进大括号
  IndentBraces: false

# 在二元运算符前换行
BreakBeforeBinaryOperators: None

# 在大括号前换行
BreakBeforeBraces: Custom

# 继承列表样式 - 优化：新增选项
BreakInheritanceList: BeforeColon

# 在三元运算符前换行
BreakBeforeTernaryOperators: true

# 在构造函数的初始化列表的逗号前换行
BreakConstructorInitializersBeforeComma: false

# 构造函数初始化列表样式 - 优化：新增选项
BreakConstructorInitializers: BeforeColon

# 每行字符的限制，0表示没有限制
ColumnLimit: 0

# 描述具有特殊意义的注释的正则表达式，它不应该被分割为多行或以其它方式改变
CommentPragmas: '^ IWYU pragma:'

# 压缩命名空间 - 优化：新增选项
CompactNamespaces: false

# 构造函数的初始化列表要么都在同一行，要么都各自一行
ConstructorInitializerAllOnOneLineOrOnePerLine: true

# 构造函数的初始化列表的缩进宽度
ConstructorInitializerIndentWidth: 4

# 延续的行的缩进宽度
ContinuationIndentWidth: 4

# 去除C++11的列表初始化的大括号{后和}前的空格
Cpp11BracedListStyle: true

# 派生指针对齐方式 - 优化：使用左对齐
DerivePointerAlignment: false

# 关闭格式化
DisableFormat: false

# 访问修饰符后的空行 - 优化：新增选项
EmptyLineAfterAccessModifier: Never

# 访问修饰符前的空行 - 优化：新增选项
EmptyLineBeforeAccessModifier: Never

# 自动检测函数的调用和定义是否被格式为每行一个参数
ExperimentalAutoDetectBinPacking: false

# 固定命名空间注释
FixNamespaceComments: true

# 需要被解读为foreach循环而不是函数调用的宏
ForEachMacros: [ foreach, Q_FOREACH, BOOST_FOREACH ]

# 函数调用参数换行位置 - 优化：新增选项
IncludeBlocks: Preserve

# 对#include进行排序，匹配了某正则表达式的#include拥有对应的优先级，匹配不到的则默认优先级为INT_MAX
# 优化：更精细的包含文件分类
IncludeCategories:
  - Regex: '^<ext/.*\.h>'
    Priority: 2
    SortPriority: 0
  - Regex: '^<.*\.h>'
    Priority: 1
    SortPriority: 0
  - Regex: '^<.*'
    Priority: 2
    SortPriority: 0
  - Regex: '.*'
    Priority: 3
    SortPriority: 0

# 缩进case标签
IndentCaseLabels: true

# 缩进goto标签 - 优化：新增选项
IndentGotoLabels: true

# 缩进预处理指令 - 优化：新增选项
IndentPPDirectives: None

# 缩进宽度
IndentWidth: 4

# 函数返回类型换行时，缩进函数声明或函数定义的函数名
IndentWrappedFunctionNames: false

# 插入尾随逗号 - 优化：新增选项
InsertTrailingCommas: None

# JavaScript引号样式 - 优化：新增选项
JavaScriptQuotes: Leave

# JavaScript导入/导出包装 - 优化：新增选项
JavaScriptWrapImports: true

# 保留在块开始处的空行
KeepEmptyLinesAtTheStartOfBlocks: false

# 开始一个块的宏的正则表达式
MacroBlockBegin: ''

# 结束一个块的宏的正则表达式
MacroBlockEnd: ''

# 连续空行的最大数量
MaxEmptyLinesToKeep: 1

# 命名空间的缩进
NamespaceIndentation: None

# ObjC块缩进宽度
ObjCBlockIndentWidth: 4

# 在ObjC的@property后添加一个空格
ObjCSpaceAfterProperty: false

# 在ObjC的protocol列表前添加一个空格
ObjCSpaceBeforeProtocolList: false

# 在call(后对函数调用换行的penalty
PenaltyBreakBeforeFirstCallParameter: 1

# 在一个注释中引入换行的penalty
PenaltyBreakComment: 300

# 第一次在<<前换行的penalty
PenaltyBreakFirstLessLess: 120

# 在一个字符串字面量中引入换行的penalty
PenaltyBreakString: 1000

# 对于每个在行字符数限制之外的字符的penalty
PenaltyExcessCharacter: 1000000

# 将函数的返回类型放到它自己的行的penalty
PenaltyReturnTypeOnItsOwnLine: 200

# 指针和引用的对齐: Left, Right, Middle - 优化：明确使用左对齐
PointerAlignment: Left

# 允许重新排版注释
ReflowComments: true

# 允许排序#include
SortIncludes: true

# 排序using声明 - 优化：新增选项
SortUsingDeclarations: true

# 在C风格类型转换后添加空格
SpaceAfterCStyleCast: false

# 在逻辑非操作符后添加空格 - 优化：新增选项
SpaceAfterLogicalNot: false

# 在模板关键字后添加空格 - 优化：新增选项
SpaceAfterTemplateKeyword: true

# 在赋值运算符之前添加空格
SpaceBeforeAssignmentOperators: true

# C++11大括号列表前添加空格 - 优化：新增选项
SpaceBeforeCpp11BracedList: false

# 构造函数初始化冒号前空格 - 优化：新增选项
SpaceBeforeCtorInitializerColon: true

# 继承冒号前空格 - 优化：新增选项
SpaceBeforeInheritanceColon: true

# 开圆括号之前添加一个空格: Never, ControlStatements, Always
SpaceBeforeParens: ControlStatements

# 指针限定符空格 - 优化：新增选项
SpaceBeforeRangeBasedForLoopColon: true

# 在空的圆括号中添加空格
SpaceInEmptyParentheses: false

# 在尾随的评论前添加的空格数(只适用于//)
SpacesBeforeTrailingComments: 2

# 在尖括号的<后和>前添加空格
SpacesInAngles: false

# 在C风格类型转换的括号中添加空格
SpacesInCStyleCastParentheses: false

# 在条件语句的括号中添加空格 - 优化：新增选项
SpacesInConditionalStatement: false

# 在容器(ObjC和JavaScript的数组和字典等)字面量中添加空格
SpacesInContainerLiterals: true

# 在圆括号的(后和)前添加空格
SpacesInParentheses: false

# 在方括号的[后和]前添加空格，lamda表达式和未指明大小的数组的声明不受影响
SpacesInSquareBrackets: false

# 标准: Cpp03, Cpp11, Auto, Latest - 优化：使用最新标准
Standard: Latest

# tab宽度
TabWidth: 4

# 使用tab字符: Never, ForIndentation, ForContinuationAndIndentation, Always
UseTab: Never