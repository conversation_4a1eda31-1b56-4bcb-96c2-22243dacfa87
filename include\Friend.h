
#pragma once

#include <iostream>
#include <string>

class Friend {
    private:
        std::string name;         // 姓名
        std::string phoneNumber;  // 手机号
        std::string address;      // 家庭地址
        std::string birthday;     // 生日
        std::string email;        // 邮箱
        std::string note;         // 备注
    public:
        Friend();
        Friend(const std::string& name, const std::string& phoneNumber,
               const std::string& address, const std::string& birthday,
               const std::string& email, const std::string& note);

        // Getters
        std::string getName() const;
        std::string getPhoneNumber() const;
        std::string getAddress() const;
        std::string getBirthday() const;
        std::string getEmail() const;
        std::string getNote() const;

        // Setters
        void setName(const std::string& name);
        void setPhoneNumber(const std::string& phoneNumber);
        void setAddress(const std::string& address);
        void setBirthday(const std::string& birthday);
        void setEmail(const std::string& email);
        void setNote(const std::string& note);

        // 打印好友信息
        void display() const;

        // 字符串化和有效性检查
        std::string toString() const;
        bool isValid() const;
};
