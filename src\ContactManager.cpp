#include "../include/ContactManager.h"
#include <memory>
#include <string>
#include "../include/DatabaseManager.h"
#include "../include/Friend.h"
#include "../include/Logger.h"

using namespace std;

ContactManager::ContactManager(std::shared_ptr<DatabaseManager> dbManager)
    : db<PERSON>anager(dbManager), currentUserId(-1), isModified(false) {
    auto logger = Logger::getInstance();
    logger->info("联系人管理系统初始化完毕");
}
// 设置当前用户
void ContactManager::setCurrentUser(int userId) {
    auto logger = Logger::getInstance();
    // 检查之前是否存在变更未保存
    if (currentUserId != -1 && isModified) {
        logger->info("正在保存用户ID " + to_string(currentUserId) + " 的通讯录变更");
        saveContacts();
    }
    currentUserId = userId;
    isModified    = false;
    logger->info("切换至用户ID " + to_string(currentUserId) + " 的通讯录");

    // 加载当前用户的通讯录
    if (!dbManager->loadFriends(friendMap, currentUserId)) {
        friendMap.clear();
        logger->mistake("无法加载用户ID " + to_string(currentUserId) + " 的通讯录");
    } else {
        logger->info("已成功加载用户ID " + to_string(currentUserId) + " 的通讯录");
    }
}

// 加载当前用户的通讯录
bool ContactManager::loadContacts() {
    auto logger = Logger::getInstance();
    if (currentUserId == -1) {
        logger->warning("无法加载通讯录：未设置当前用户");
        return false;
    }

    bool wasModified = isModified;  // 保存修改标记
    if (!dbManager->loadFriends(friendMap, currentUserId)) {
        logger->mistake("从数据库加载用户ID " + to_string(currentUserId) + " 的通讯录失败");
        return false;
    }

    isModified = wasModified;  // 恢复修改标记
    logger->info("已重新加载用户ID " + to_string(currentUserId) + " 的通讯录，共 " + to_string(friendMap.size()) + " 位联系人");
    return true;
}

// 保存通讯录到数据源
bool ContactManager::saveContacts() {
    auto logger = Logger::getInstance();
    if (currentUserId == -1) {
        logger->warning("无法保存通讯录：未设置当前用户");
        return false;
    }
    if (!isModified) {
        logger->info("通讯录未发生变更，无需保存");
        return true;
    }
    if (!dbManager->saveFriends(friendMap, currentUserId)) {
        logger->mistake("保存用户ID " + to_string(currentUserId) + " 的通讯录失败");
        return false;
    }

    isModified = false;
    logger->info("已成功保存用户ID " + to_string(currentUserId) + " 的通讯录，共 " +
                 to_string(friendMap.size()) + " 位联系人");
    return true;
}

// 添加好友
bool ContactManager::addFriend(const Friend& friend_) {
    if (currentUserId == -1) {
        return false;
    }

    if (friend_.getPhoneNumber().empty()) {
        return false;  // 如果电话号码为空，则不添加
    }

    if (friendMap.find(friend_.getPhoneNumber()) != friendMap.end()) {
        return false;  // 如果通讯录中已经存在该好友，则不添加
    }

    friendMap[friend_.getPhoneNumber()] = friend_;  // 将好友添加到通讯录
    isModified = true;     // 添加好友后，设置修改标记

    return true;
}

// 删除好友
bool ContactManager::deleteFriend(const std::string& phoneNumber) {
    if (currentUserId == -1) {
        return false;
    }

    auto it = friendMap.find(phoneNumber);
    if (it == friendMap.end()) {
        return false;
    }

    friendMap.erase(it);
    isModified = true;

    return true;
}

// 修改好友信息
bool ContactManager::updateFriend(const std::string& phoneNumber, const Friend& updatedFriend) {
    auto logger = Logger::getInstance();

    if (currentUserId == -1) {
        logger->warning("无法更新联系人：未设置当前用户");
        return false;
    }
    auto it = friendMap.find(phoneNumber);
    if (it == friendMap.end()) {
        logger->warning("无法更新联系人：电话号码 " + phoneNumber + " 不存在");
        return false;
    }

    string oldName  = it->second.getName();
    string newName  = updatedFriend.getName();
    string newPhone = updatedFriend.getPhoneNumber();
    if (updatedFriend.getPhoneNumber() != phoneNumber) {
        if (friendMap.find(updatedFriend.getPhoneNumber()) != friendMap.end()) {
            logger->warning("无法更新联系人：新电话号码 " + newPhone + " 已被其他联系人使用");
            return false;
        }
        friendMap.erase(it);
        logger->info("联系人 " + oldName + " 的电话号码已从 " + phoneNumber + " 更改为 " + newPhone);
    }
    friendMap[newPhone] = updatedFriend;  // 更新好友信息
    isModified          = true;           // 修改好友信息后，设置修改标记

    if (oldName != newName) {
        logger->info("联系人 " + oldName + " 的姓名已从 " + oldName + " 更改为 " + newName);
    }

    logger->info("联系人 " + updatedFriend.getName() + "（电话: " + updatedFriend.getPhoneNumber() + ")信息已更新");
    return true;
}

// 用手机号查询好友信息
Friend* ContactManager::getFriend(const std::string& phoneNumber) {
    auto logger = Logger::getInstance();

    if (currentUserId == -1) {
        logger->warning("无法查询联系人：未设置当前用户");
        return nullptr;
    }

    auto it = friendMap.find(phoneNumber);
    if (it == friendMap.end()) {
        logger->debug("查询联系人：电话号码 " + phoneNumber + " 不存在");
        return nullptr;  // 如果通讯录中不存在该好友，则返回空指针
    }

    logger->debug("查询联系人成功：" + it->second.getName() + "，电话: " + phoneNumber);
    return &(it->second);
}

// 按照姓名搜索好友（可能返回多个结果）
std::vector<Friend> ContactManager::searchFriendsByName(const std::string& name) {
    auto logger = Logger::getInstance();

    if (currentUserId == -1) {
        logger->warning("无法搜索联系人：未设置当前用户");
        return {};
    }

    vector<Friend> result;
    for (const auto& pair : friendMap) {
        if (pair.second.getName() == name) {
            result.push_back(pair.second);
        }
    }

    logger->info("按姓名 '" + name + "' 搜索联系人，找到 " + to_string(result.size()) + " 个结果");
    return result;
}

// 获取所有好友
const std::map<std::string, Friend>& ContactManager::getAllFriends() const {
    return friendMap;
}

// 检查是否有修改
bool ContactManager::hasModifications() const {
    return isModified;
}

// 重置修改标记
void ContactManager::resetModificationFlag() {
    auto logger = Logger::getInstance();

    if (isModified) {
        logger->debug("重置通讯录修改标记");
        isModified = false;
    }
}

// 清空通讯录
void ContactManager::clear() {
    auto logger = Logger::getInstance();

    if (currentUserId == -1) {
        logger->warning("无法清空通讯录：未设置当前用户");
        return;
    }

    size_t count = friendMap.size();
    friendMap.clear();
    isModified = true;

    logger->warning("已清空用户ID " + to_string(currentUserId) + " 的通讯录，删除了 " +
                    to_string(count) + " 位联系人");
}

// 获取好友数量
size_t ContactManager::getContactCount() const {
    return friendMap.size();
}