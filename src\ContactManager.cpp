#include "../include/ContactManager.h"
#include <memory>
#include <string>
#include "../include/DatabaseManager.h"
#include "../include/Friend.h"

using namespace std;

ContactManager::ContactManager(std::shared_ptr<DatabaseManager> dbManager)
    : db<PERSON>anager(dbManager), currentUserId(-1), isModified(false) {
    // 移除日志记录，由调用方处理
}
// 设置当前用户（纯功能，返回操作结果）
bool ContactManager::setCurrentUser(int userId) {
    // 检查之前是否存在变更未保存
    if (currentUserId != -1 && isModified) {
        if (!saveContacts()) {
            return false;  // 保存失败
        }
    }
    currentUserId = userId;
    isModified    = false;

    // 加载当前用户的通讯录
    if (!dbManager->loadFriends(friendMap, currentUserId)) {
        friendMap.clear();
        return false;  // 加载失败
    }
    return true;  // 成功
}

// 加载当前用户的通讯录（纯功能）
bool ContactManager::loadContacts() {
    if (currentUserId == -1) {
        return false;  // 未设置当前用户
    }

    bool wasModified = isModified;  // 保存修改标记
    if (!dbManager->loadFriends(friendMap, currentUserId)) {
        return false;  // 加载失败
    }

    isModified = wasModified;  // 恢复修改标记
    return true;               // 加载成功
}

// 保存通讯录到数据源（纯功能）
bool ContactManager::saveContacts() {
    if (currentUserId == -1) {
        return false;  // 未设置当前用户
    }
    if (!isModified) {
        return true;  // 无需保存
    }
    if (!dbManager->saveFriends(friendMap, currentUserId)) {
        return false;  // 保存失败
    }

    isModified = false;
    return true;  // 保存成功
}

// 添加好友
bool ContactManager::addFriend(const Friend& friend_) {
    if (currentUserId == -1) {
        return false;
    }

    if (friend_.getPhoneNumber().empty()) {
        return false;  // 如果电话号码为空，则不添加
    }

    if (friendMap.find(friend_.getPhoneNumber()) != friendMap.end()) {
        return false;  // 如果通讯录中已经存在该好友，则不添加
    }

    friendMap[friend_.getPhoneNumber()] = friend_;  // 将好友添加到通讯录
    isModified                          = true;     // 添加好友后，设置修改标记

    return true;
}

// 删除好友
bool ContactManager::deleteFriend(const std::string& phoneNumber) {
    if (currentUserId == -1) {
        return false;
    }

    auto it = friendMap.find(phoneNumber);
    if (it == friendMap.end()) {
        return false;
    }

    friendMap.erase(it);
    isModified = true;

    return true;
}

// 修改好友信息（纯功能）
bool ContactManager::updateFriend(const std::string& phoneNumber, const Friend& updatedFriend) {
    if (currentUserId == -1) {
        return false;  // 未设置当前用户
    }
    auto it = friendMap.find(phoneNumber);
    if (it == friendMap.end()) {
        return false;  // 联系人不存在
    }

    // 检查新电话号码是否与其他联系人冲突
    if (updatedFriend.getPhoneNumber() != phoneNumber) {
        if (friendMap.find(updatedFriend.getPhoneNumber()) != friendMap.end()) {
            return false;  // 新电话号码已被使用
        }
        friendMap.erase(it);
    }
    friendMap[updatedFriend.getPhoneNumber()] = updatedFriend;  // 更新好友信息
    isModified                                = true;           // 设置修改标记

    return true;  // 更新成功
}

// 用手机号查询好友信息（纯功能）
Friend* ContactManager::getFriend(const std::string& phoneNumber) {
    if (currentUserId == -1) {
        return nullptr;  // 未设置当前用户
    }

    auto it = friendMap.find(phoneNumber);
    if (it == friendMap.end()) {
        return nullptr;  // 联系人不存在
    }

    return &(it->second);  // 返回联系人指针
}

// 按照姓名搜索好友（纯功能）
std::vector<Friend> ContactManager::searchFriendsByName(const std::string& name) {
    if (currentUserId == -1) {
        return {};  // 未设置当前用户
    }

    vector<Friend> result;
    for (const auto& pair : friendMap) {
        if (pair.second.getName() == name) {
            result.push_back(pair.second);
        }
    }

    return result;  // 返回搜索结果
}

// 获取所有好友
const std::map<std::string, Friend>& ContactManager::getAllFriends() const {
    return friendMap;
}

// 检查是否有修改
bool ContactManager::hasModifications() const {
    return isModified;
}

// 重置修改标记（纯功能）
void ContactManager::resetModificationFlag() {
    isModified = false;
}

// 清空通讯录（纯功能）
bool ContactManager::clear() {
    if (currentUserId == -1) {
        return false;  // 未设置当前用户
    }

    friendMap.clear();
    isModified = true;
    return true;  // 清空成功
}

// 获取好友数量
size_t ContactManager::getContactCount() const {
    return friendMap.size();
}