#ifndef USER_H
#define USER_H

#pragma once

#include <iostream>
#include <memory>
#include <string>

class UserManager;
class Logger;

// 用户基类
class User {
    protected:
        int         userId;
        std::string username;
        std::string passwordHash;
        bool        isLoggedIn;
        bool        islocked = false;
    public:
        User(int userId, const std::string& username, const std::string& passwordHash);
        virtual ~User() = default;

        // 基本的getter方法
        std::string getUsername() const;
        std::string getPasswordHash() const;
        int         getUserId() const;
        bool        getLoginStatus() const;
        bool        getLockStatus() const;

        // 设置登录状态
        void setLoginStatus(bool status);
        void setLockStatus(bool status);

        // 设置密码哈希
        void setPasswordHash(const std::string& newPasswordHash);

        // 虚函数以支持多态
        virtual std::string getUserType() const = 0;
        virtual std::string getDisplayInfo() const;
};

// 普通用户类
class RegularUser : public User {
    public:
        RegularUser(int userId, const std::string& username, const std::string& passwordHash);
        std::string getUserType() const override;
};

// 管理员用户类
class AdminUser : public User {
    public:
        AdminUser(int userId, const std::string& username, const std::string& passwordHash);
        std::string getUserType() const override;

        // 管理员特有方法
        void                     viewAllUsers() const;
        std::vector<std::string> getSystemLogs() const;
};

// 用户工厂类 - 实现工厂模式
class UserFactory {
    public:
        static std::shared_ptr<User> createUser(int                userId,
                                                const std::string& username,
                                                const std::string& passwordHash,
                                                const std::string& userType);
};

#endif  // USER_H
