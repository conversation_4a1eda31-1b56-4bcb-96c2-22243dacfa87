#pragma once
#include <F:/MySQL/MySQL Server 5.7/include/mysql.h>
#include <memory>
#include <mutex>
#include <string>
#include <vector>

// 日志级别枚举
enum class LogLevel {
    INFO,
    WARNING,
    MISTAKE,
    DEBUG
};

// 日志事件类
class LogEvent {
    private:
        std::string message;
        LogLevel    level;
        std::string timestamp;
        std::string username;
    public:
        LogEvent(const std::string& message, LogLevel level, const std::string& username);
        std::string getMessage() const;
        LogLevel    getLevel() const;
        std::string getTimestamp() const;
        std::string getUsername() const;
        std::string getLevelString() const;
};

// 终端日志处理类
class ConsoleLogger {
    public:
        void logEvent(const LogEvent& event);
};

// 数据库日志处理类
class DatabaseLogger {
    private:
        MYSQL*      mysql;
        std::string host;
        std::string user;
        std::string password;
        std::string database;
        int         port;

        bool connect();
        void disconnect();
        bool reconnectDatabase();
    public:
        DatabaseLogger(const std::string& host,
                       const std::string& user,
                       const std::string& password,
                       const std::string& database,
                       int                port);
        ~DatabaseLogger();
        void                     logEvent(const LogEvent& event);
        std::vector<std::string> getHistoryLogs();  // 获取历史日志
};

// 日志管理器 - 单例模式，仅数据库存储
class Logger {
    private:
        static std::shared_ptr<Logger>  instance;
        static std::mutex               mutex_;
        std::shared_ptr<DatabaseLogger> dbLogger;
        std::shared_ptr<ConsoleLogger>  consoleLogger;

        Logger();
        Logger(const Logger&)            = delete;
        Logger& operator=(const Logger&) = delete;
    public:
        static std::shared_ptr<Logger> getInstance();

        // 日志记录方法
        void log(const std::string& message, LogLevel level, const std::string& username = "System");
        void info(const std::string& message, const std::string& username = "System");
        void warning(const std::string& message, const std::string& username = "System");
        void mistake(const std::string& message, const std::string& username = "System");
        void debug(const std::string& message, const std::string& username = "System");

        // 获取历史日志
        std::vector<std::string> getHistoryLogs();
};
