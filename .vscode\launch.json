{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug MyContact",
      "type": "cppdbg",
      "request": "launch",
      "program": "${workspaceFolder}/build/${workspaceFolderBasename}.exe", // 修正变量，确保可执行文件路径正确
      "args": [],
      "stopAtEntry": false, // 启动时停在main函数，便于调试
      "cwd": "${workspaceFolder}",
      "environment": [],
      "externalConsole": true, // 推荐为true，便于交互式程序调试
      "MIMode": "gdb",
      "miDebuggerPath": "F:/msys64/mingw64/bin/gdb.exe", // gdb路径
      "setupCommands": [
        {
          "description": "Enable pretty-printing for gdb",
          "text": "-enable-pretty-printing",
          "ignoreFailures": true
        }
      ],
      "preLaunchTask": "C/C++: build active file", // 调试前自动编译
    }
  ]
}