{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "clean build",
            "type": "shell",
            "command": "if (Test-Path build/myContact.exe) { Remove-Item build/myContact.exe }",
            "problemMatcher": []
        },
        {
            "label": "C/C++: build active file",
            "type": "process",
            "command": "F:/msys64/mingw64/bin/g++.exe", // 修改为你的g++路径
            "args": [
                "-fdiagnostics-color=always",
                "-g",
                "${workspaceFolder}/src/*.cpp",
                "-I",
                "${workspaceFolder}/include",
                "-I",
                "F:/MySQL/MySQL Server 5.7/include",
                "-L",
                "F:/MySQL/MySQL Server 5.7/lib",
                "-llibmysql",
                "-o",
                "${workspaceFolder}/build/${workspaceFolderBasename}.exe"
            ],
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "detail": "compiler: F:/msys64/mingw64/bin/g++.exe",
            "problemMatcher": {
                "owner": "cpp",
                "pattern": {
                    "regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$",
                    "file": 1,
                    "line": 2,
                    "column": 3,
                    "severity": 4,
                    "message": 5
                }
            },
            "dependsOn": [
                "clean build"
            ]
        }
    ]
}