
#include <F:/MySQL/MySQL Server 5.7/include/mysql.h>
#include <map>
#include <memory>
#include <string>
#include <vector>

// 前向声明，减少头文件依赖
class Friend;
class User;

// 数据存储策略接口
class StorageStrategy {
    public:
        virtual ~StorageStrategy() = default;

        // 好友数据操作
        virtual bool saveFriends(const std::map<std::string, Friend>& friendMap, int userId) = 0;
        virtual bool loadFriends(std::map<std::string, Friend>& friendMap, int userId)       = 0;

        // 用户数据操作
        virtual bool                               saveUser(const std::shared_ptr<User>& user) = 0;
        virtual std::shared_ptr<User>              loadUser(const std::string& username)       = 0;
        virtual std::vector<std::shared_ptr<User>> loadAllUsers()                              = 0;
        virtual bool                               deleteUser(int userId)                      = 0;

        // 验证用户凭据
        virtual bool validateCredentials(const std::string& username, const std::string& passwordHash) = 0;
};

// 文件存储策略实现
class FileStorageStrategy : public StorageStrategy {
    private:
        std::string dataPath;
    public:
        explicit FileStorageStrategy(const std::string& path);

        bool saveFriends(const std::map<std::string, Friend>& friendMap, int userId) override;
        bool loadFriends(std::map<std::string, Friend>& friendMap, int userId) override;

        bool                               saveUser(const std::shared_ptr<User>& user) override;
        std::shared_ptr<User>              loadUser(const std::string& username) override;
        std::vector<std::shared_ptr<User>> loadAllUsers() override;
        bool                               deleteUser(int userId) override;

        bool validateCredentials(const std::string& username, const std::string& passwordHash) override;
};

// MySQL数据库存储策略实现
class MySQLStorageStrategy : public StorageStrategy {
    private:
        std::string host, user, password, database;
        int         port;
        MYSQL*      mysql = nullptr;
        bool        connect();
        void        disconnect();
        bool        reconnectDatabase();
        std::string escapeString(const std::string& str); // 转义SQL字符串，防止SQL注入
    public:
        MySQLStorageStrategy(const std::string& host,
                             const std::string& user,
                             const std::string& password,
                             const std::string& database,
                             int                port);

        bool saveFriends(const std::map<std::string, Friend>& friendMap, int userId) override;
        bool loadFriends(std::map<std::string, Friend>& friendMap, int userId) override;

        bool                               saveUser(const std::shared_ptr<User>& user) override;
        std::shared_ptr<User>              loadUser(const std::string& username) override;
        std::vector<std::shared_ptr<User>> loadAllUsers() override;
        bool                               deleteUser(int userId) override;

        bool validateCredentials(const std::string& username, const std::string& passwordHash) override;
};

// 数据库管理器类
class DatabaseManager {
    private:
        std::shared_ptr<StorageStrategy> strategy;
    public:
        DatabaseManager(std::shared_ptr<StorageStrategy> strategy);

        void setStrategy(std::shared_ptr<StorageStrategy> strategy);

        // 保存好友信息
        bool saveFriends(const std::map<std::string, Friend>& friendMap, int userId);

        // 加载好友信息
        bool loadFriends(std::map<std::string, Friend>& friendMap, int userId);

        // 保存用户信息
        bool saveUser(const std::shared_ptr<User>& user);

        // 加载用户信息
        std::shared_ptr<User> loadUser(const std::string& username);

        // 加载所有用户信息
        std::vector<std::shared_ptr<User>> loadAllUsers();

        // 验证用户凭据
        bool validateCredentials(const std::string& username, const std::string& passwordHash);

        // 删除用户
        bool deleteUser(int userId);
};
