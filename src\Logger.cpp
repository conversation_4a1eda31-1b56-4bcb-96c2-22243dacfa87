
#include "../include/Logger.h"
#include <windows.h>
#include <ctime>
#include <iostream>
#include <iomanip>

using namespace std;

//-------- LogEvent 实现 --------
LogEvent::LogEvent(const std::string& message, LogLevel level, const std::string& username) : message(message),
                                                                                              level(level),
                                                                                              username(username) {
    // 生成时间戳
    std::time_t now = std::time(nullptr);
    char        buffer[80];
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", std::localtime(&now));
    timestamp = buffer;
}
std::string LogEvent::getMessage() const {
    return message;
}
LogLevel LogEvent::getLevel() const {
    return level;
}
std::string LogEvent::getTimestamp() const {
    return timestamp;
}
std::string LogEvent::getUsername() const {
    return username;
}
std::string LogEvent::getLevelString() const {
    switch (level) {
        case LogLevel::INFO:
            return "[信息]";
        case LogLevel::WARNING:
            return "[警告]";
        case LogLevel::MISTAKE:
            return "[错误]";
        case LogLevel::DEBUG:
            return "[调试]";
        default:
            return "[未知]";
    }
}

// ----------- ConsoleLogger 实现 -----------
void ConsoleLogger::logEvent(const LogEvent& event) {
    // 根据日志级别使用不同颜色
    std::string colorCode;
    std::string resetCode = "\033[0m";  // ANSI 颜色重置代码

    switch (event.getLevel()) {
        case LogLevel::INFO:
            colorCode = "\033[32m";  // 绿色
            break;
        case LogLevel::WARNING:
            colorCode = "\033[33m";  // 黄色
            break;
        case LogLevel::MISTAKE:
            colorCode = "\033[31m";  // 红色
            break;
        case LogLevel::DEBUG:
            colorCode = "\033[36m";  // 青色
            break;
        default:
            colorCode = "\033[0m";  // 默认
    }
    
    // 简化的日志输出格式
    static std::string lastTimestamp = "";
    static int messageCount = 0;
    
    // 如果时间戳相同，使用更紧凑的格式
    if (event.getTimestamp() == lastTimestamp) {
        messageCount++;
        std::cout << colorCode << "  → " << std::left << std::setw(12) 
                  << event.getLevelString() << event.getMessage() << resetCode << std::endl;
    } else {
        // 如果是新的时间戳，显示完整头部
        if (messageCount > 0) {
            std::cout << "------------------------------------------------" << std::endl;
        }
        std::cout << colorCode << event.getTimestamp() << " " << event.getLevelString() 
                  << " [" << event.getUsername() << "] " << event.getMessage() << resetCode << std::endl;
        lastTimestamp = event.getTimestamp();
        messageCount = 1;
    }
}

// -------- 数据库日志实现 --------
DatabaseLogger::DatabaseLogger(const std::string& host,
                               const std::string& user,
                               const std::string& password,
                               const std::string& database,
                               int                port)
    : host(host), user(user), password(password), database(database), port(port) {
    connect();
}
DatabaseLogger::~DatabaseLogger() {
    disconnect();
}
bool DatabaseLogger::connect() {
    try {
        cout << "正在连接到数据库日志系统..." << std::endl;
        // 初始化数据库连接
        mysql = mysql_init(nullptr);
        if (mysql == nullptr) {
            throw runtime_error("MySQL初始化失败");
            return false;
        }

        // 连接到数据库
        if (!reconnectDatabase()) {
            throw std::runtime_error("无法连接到MySQL数据库");
            return false;
        }
        mysql_set_character_set(mysql, "utf8mb4");

        cout << "数据库日志系统连接成功" << std::endl;
        return true;
    } catch (const std::exception& e) {
        cerr << "数据库日志系统连接失败: " << e.what() << std::endl;
        if (mysql) {
            mysql_close(mysql);
            mysql = nullptr;
        }
        return false;
    }
}
void DatabaseLogger::disconnect() {
    if (mysql) {
        mysql_close(mysql);
        mysql = nullptr;
        cout << "数据库日志系统连接已关闭" << std::endl;
    }
}
bool DatabaseLogger::reconnectDatabase() {
    if (mysql != nullptr) {
        // 检查连接是否有效
        if (mysql_ping(mysql) == 0) {
            return true;  // 连接有效
        }

        // 连接失效，关闭旧连接
        mysql_close(mysql);
        mysql = mysql_init(nullptr);
        if (mysql == nullptr) {
            return false;
        }
    }

    // 建立新连接
    if (!mysql_real_connect(mysql, host.c_str(), user.c_str(), password.c_str(),
                            database.c_str(), port, nullptr, 0)) {
        return false;
    }

    // 设置字符集
    mysql_set_character_set(mysql, "utf8mb4");
    return true;
}

void DatabaseLogger::logEvent(const LogEvent& event) {
    try {
        // 构建格式化的日志消息 - 需要转义单引号
        std::string message = event.getMessage();
        std::string level = event.getLevelString();
        std::string username = event.getUsername();
        std::string timestamp = event.getTimestamp();
        
        // 替换所有单引号为两个单引号，这是MySQL中转义单引号的标准方式
        auto escapeSQL = [](std::string& str) {
            size_t pos = 0;
            while((pos = str.find("'", pos)) != std::string::npos) {
                str.replace(pos, 1, "''");
                pos += 2;
            }
        };
        
        escapeSQL(message);
        escapeSQL(level);
        escapeSQL(username);
        
        std::string logEntry = "INSERT INTO logs (message, level, username, timestamp) VALUES ('" + 
                               message + "', '" + 
                               level + "', '" + 
                               username + "', '" + 
                               timestamp + "');";

        // 实际执行SQL语句将日志写入数据库
        if (mysql != nullptr && mysql_ping(mysql) == 0) {
            // 数据库连接有效，直接执行SQL
            if (mysql_query(mysql, logEntry.c_str()) != 0) {
                std::cerr << "执行日志SQL语句失败: " << mysql_error(mysql) << std::endl;
            }
        } else {
            // 数据库连接无效，尝试重新连接
            reconnectDatabase();
            if (mysql != nullptr && mysql_ping(mysql) == 0) {
                if (mysql_query(mysql, logEntry.c_str()) != 0) {
                    std::cerr << "执行日志SQL语句失败: " << mysql_error(mysql) << std::endl;
                }
            } else {
                std::cerr << "数据库连接失败，无法写入日志" << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "向数据库写入日志时发生错误: " << e.what() << std::endl;
    }
}
std::vector<std::string> DatabaseLogger::getHistoryLogs() {
    std::vector<std::string> logs;

    try {
        // 检查数据库连接
        if (mysql == nullptr || mysql_ping(mysql) != 0) {
            if (!reconnectDatabase()) {
                std::cerr << "无法连接到数据库，获取历史日志失败" << std::endl;
                return logs;
            }
        }

        // 执行查询语句
        const char* query = "SELECT timestamp, level, username, message FROM logs ORDER BY timestamp DESC";
        if (mysql_query(mysql, query) != 0) {
            std::cerr << "执行查询历史日志SQL失败: " << mysql_error(mysql) << std::endl;
            return logs;
        }

        // 获取查询结果
        MYSQL_RES* result = mysql_store_result(mysql);
        if (result == nullptr) {
            std::cerr << "获取查询结果失败: " << mysql_error(mysql) << std::endl;
            return logs;
        }

        // 处理查询结果
        MYSQL_ROW row;
        while ((row = mysql_fetch_row(result)) != nullptr) {
            std::string logEntry = std::string(row[0]) + " " +   // timestamp
                                   std::string(row[1]) + " [" +  // level
                                   std::string(row[2]) + "] " +  // username
                                   std::string(row[3]);          // message
            logs.push_back(logEntry);
        }

        // 释放结果集
        mysql_free_result(result);

        std::cout << "成功获取 " << logs.size() << " 条历史日志记录" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "获取历史日志时发生错误: " << e.what() << std::endl;
    }

    return logs;
}

// -------- Logger 单例实现 --------
std::shared_ptr<Logger> Logger::instance = nullptr;
std::mutex              Logger::mutex_;

Logger::Logger() {
    dbLogger      = std::make_shared<DatabaseLogger>("localhost", "root", "78963.", "contact_db", 3306);
    consoleLogger = std::make_shared<ConsoleLogger>();
    cout << "日志系统初始化完成" << std::endl;
}
shared_ptr<Logger> Logger::getInstance() {
    lock_guard<mutex> lock(mutex_);
    if (!instance) {
        // 由于Logger构造函数是私有的，无法使用make_shared
        // 必须使用raw new并自己包装为shared_ptr
        instance = shared_ptr<Logger>(new Logger());
    }
    return instance;
}
// 日志记录方法
void Logger::log(const std::string& message, LogLevel level, const std::string& username) {
    LogEvent event(message, level, username);
    consoleLogger->logEvent(event);
    dbLogger->logEvent(event);
}

void Logger::info(const std::string& message, const std::string& username) {
    log(message, LogLevel::INFO, username);
}

void Logger::warning(const std::string& message, const std::string& username) {
    log(message, LogLevel::WARNING, username);
}

void Logger::mistake(const std::string& message, const std::string& username) {
    log(message, LogLevel::MISTAKE, username);
}

void Logger::debug(const std::string& message, const std::string& username) {
    log(message, LogLevel::DEBUG, username);
}

std::vector<std::string> Logger::getHistoryLogs() {
    return dbLogger->getHistoryLogs();
}
