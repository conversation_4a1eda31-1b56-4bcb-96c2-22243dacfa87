#include "../include/User.h"
#include <memory>
#include <string>
#include "../include/DatabaseManager.h"
#include "../include/Logger.h"

using namespace std;

User::User(int userId, const string& username, const string& passwordHash) : userId(userId), username(username), passwordHash(passwordHash) {}
string User::getUsername() const {
    return this->username;
}
string User::getPasswordHash() const {
    return this->passwordHash;
}
int User::getUserId() const {
    return this->userId;
}
bool User::getLoginStatus() const {
    return this->isLoggedIn;
}
bool User::getLockStatus() const {
    return this->islocked;
}
void User::setLoginStatus(bool status) {
    isLoggedIn = status;
}
void User::setLockStatus(bool status) {
    islocked = status;
}

void User::setPasswordHash(const std::string& newPasswordHash) {
    passwordHash = newPasswordHash;
}
void User::display() const {
    cout << "用户ID：" << userId << endl;
    cout << "用户名：" << username << endl;
    cout << "用户类型：" << getUserType() << endl;
}
RegularUser::RegularUser(int userId, const string& username, const string& passwordHash) : User(userId, username, passwordHash) {}
string RegularUser::getUserType() const {
    return "regular";  // 返回用户类型
}

AdminUser::AdminUser(int userId, const string& username, const string& passwordHash) : User(userId, username, passwordHash) {}
string AdminUser::getUserType() const {
    return "admin";  // 返回用户类型
}
void AdminUser::viewAllUsers() const {
    // 不再在这里创建DatabaseManager实例，而是使用外部传入的实例
    auto logger = Logger::getInstance();
    logger->info("管理员正在查看所有用户");
    
    // 这里我们不再进行任何数据库操作，只提供一个空的实现
    // 具体的用户列表获取和显示逻辑应该在MenuSystem中处理
}

void AdminUser::viewLogs() const {
    auto logger = Logger::getInstance();
    if (!logger) {
        logger->mistake("无法获取日志实例！");
        return;
    }
    cout << "【管理员操作】系统日志列表：" << endl;
    auto logs = logger->getHistoryLogs();
    if (logs.empty()) {
        logger->info("暂无日志记录");
    } else {
        for (const auto& log : logs) {
            cout << log << endl;
        }
    }
    logger->info("查询日志成功，共计 " + std::to_string(logs.size()) + " 条日志。");
}

// 用户工厂类静态方法 - 创建用户对象
shared_ptr<User> UserFactory::createUser(int userId, const string& username, const string& passwordHash, const string& userType) {
    if (userType == "regular") {
        return make_shared<RegularUser>(userId, username, passwordHash);
    } else if (userType == "admin") {
        return make_shared<AdminUser>(userId, username, passwordHash);
    }
    // 默认创建普通用户
    return make_shared<RegularUser>(userId, username, passwordHash);
}