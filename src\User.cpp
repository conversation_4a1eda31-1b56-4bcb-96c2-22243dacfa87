#include "../include/User.h"
#include <memory>
#include <string>
#include "../include/DatabaseManager.h"
#include "../include/Logger.h"

using namespace std;

User::User(int userId, const string& username, const string& passwordHash) : userId(userId), username(username), passwordHash(passwordHash) {}
string User::getUsername() const {
    return this->username;
}
string User::getPasswordHash() const {
    return this->passwordHash;
}
int User::getUserId() const {
    return this->userId;
}
bool User::getLoginStatus() const {
    return this->isLoggedIn;
}
bool User::getLockStatus() const {
    return this->islocked;
}
void User::setLoginStatus(bool status) {
    isLoggedIn = status;
}
void User::setLockStatus(bool status) {
    islocked = status;
}

void User::setPasswordHash(const std::string& newPasswordHash) {
    passwordHash = newPasswordHash;
}
// 获取用户显示信息（纯数据返回，不包含输出操作）
std::string User::getDisplayInfo() const {
    return "用户ID：" + std::to_string(userId) + "\n" +
           "用户名：" + username + "\n" +
           "用户类型：" + getUserType();
}
RegularUser::RegularUser(int userId, const string& username, const string& passwordHash) : User(userId, username, passwordHash) {}
string RegularUser::getUserType() const {
    return "regular";  // 返回用户类型
}

AdminUser::AdminUser(int userId, const string& username, const string& passwordHash) : User(userId, username, passwordHash) {}
string AdminUser::getUserType() const {
    return "admin";  // 返回用户类型
}
void AdminUser::viewAllUsers() const {
    // 不再在这里创建DatabaseManager实例，而是使用外部传入的实例
    auto logger = Logger::getInstance();
    logger->info("管理员正在查看所有用户");

    // 这里我们不再进行任何数据库操作，只提供一个空的实现
    // 具体的用户列表获取和显示逻辑应该在MenuSystem中处理
}

// 获取系统日志（纯数据返回，不包含输出和日志记录操作）
std::vector<std::string> AdminUser::getSystemLogs() const {
    auto logger = Logger::getInstance();
    if (!logger) {
        return {};  // 返回空向量表示获取失败
    }
    return logger->getHistoryLogs();
}

// 用户工厂类静态方法 - 创建用户对象
shared_ptr<User> UserFactory::createUser(int userId, const string& username, const string& passwordHash, const string& userType) {
    if (userType == "regular") {
        return make_shared<RegularUser>(userId, username, passwordHash);
    } else if (userType == "admin") {
        return make_shared<AdminUser>(userId, username, passwordHash);
    }
    // 默认创建普通用户
    return make_shared<RegularUser>(userId, username, passwordHash);
}