#pragma once

#include <map>
#include <memory>
#include <string>
#include <vector>
#include "Friend.h"

class DatabaseManager;

class ContactManager {
    public:
        // 构造函数
        ContactManager(std::shared_ptr<DatabaseManager> dbManager);

        // 设置当前用户
        bool setCurrentUser(int userId);

        // 加载当前用户的通讯录
        bool loadContacts();

        // 保存通讯录到数据源
        bool saveContacts();

        // 添加好友
        bool addFriend(const Friend& friend_);

        // 删除好友
        bool deleteFriend(const std::string& phoneNumber);

        // 修改好友信息
        bool updateFriend(const std::string& phoneNumber, const Friend& updatedFriend);

        // 用手机号查询好友信息
        Friend* getFriend(const std::string& phoneNumber);

        // 按照姓名搜索好友（可能返回多个结果）
        std::vector<Friend> searchFriendsByName(const std::string& name);

        // 获取所有好友
        const std::map<std::string, Friend>& getAllFriends() const;

        // 检查是否有修改
        bool hasModifications() const;

        // 重置修改标记
        void resetModificationFlag();

        // 清空通讯录
        bool clear();

        // 获取好友数量
        size_t getContactCount() const;

        // 获取当前用户ID
        int getCurrentUserId() const;
    private:
        std::shared_ptr<DatabaseManager> dbManager;      // 数据库管理器
        std::map<std::string, Friend>    friendMap;      // 好友列表，以电话号码为键
        int                              currentUserId;  // 当前用户ID
        bool                             isModified;     // 通讯录是否被修改
};