#include "../include/DatabaseManager.h"
#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <vector>
#include "../include/ContactManager.h"
#include "../include/Friend.h"
#include "../include/Logger.h"
#include "../include/User.h"

using namespace std;

//------------- 文件存储策略类 ---------------
FileStorageStrategy::FileStorageStrategy(const std::string& path) : dataPath(path) {}

bool FileStorageStrategy::saveFriends(const std::map<std::string, Friend>& friendMap, int userId) {
    auto logger = Logger::getInstance();
    logger->info("正在用户ID" + to_string(userId) + " 的好友信息到文件存储");

    string   filePath = dataPath + "/friends_" + to_string(userId) + ".txt";
    ofstream outfile(filePath);
    if (!outfile.is_open()) {
        logger->mistake("无法打开好友信息文件：" + filePath);
        return false;
    }

    for (const auto& pair : friendMap) {
        const Friend& friendInfo = pair.second;
        outfile << friendInfo.getPhoneNumber() << ","
                << friendInfo.getName() << ","
                << friendInfo.getAddress() << ","
                << friendInfo.getEmail() << ","
                << friendInfo.getBirthday() << ","
                << friendInfo.getNote() << endl;
    }
    outfile.close();

    logger->info("成功保存用户ID " + to_string(userId) + " 的 " + to_string(friendMap.size()) + " 位好友信息");
    return true;
}
bool FileStorageStrategy::loadFriends(std::map<std::string, Friend>& friendMap, int userId) {
    auto logger = Logger::getInstance();
    logger->info("正在从文件存储加载用户ID " + to_string(userId) + " 的好友信息");

    string   filePath = dataPath + "/friends_" + to_string(userId) + ".txt";
    ifstream infile(filePath);
    if (!infile.is_open()) {
        logger->mistake("好友信息文件不存在或无法打开:" + filePath);
        return false;
    }

    friendMap.clear();
    string line;
    int    friendCount = 0;
    while (getline(infile, line)) {
        stringstream   ss(line);
        string         token;
        vector<string> tokens;
        while (getline(ss, token, ',')) {
            tokens.push_back(token);
        }

        if (tokens.size() == 6) {
            Friend friendInfo(tokens[0], tokens[1], tokens[2], tokens[3], tokens[4], tokens[5]);
            friendMap[friendInfo.getPhoneNumber()] = friendInfo;
            friendCount++;
        }
    }
    infile.close();

    logger->info("成功从文件存储加载用户ID " + to_string(userId) + " 的 " + to_string(friendCount) + " 位好友信息");
    return true;
}

bool FileStorageStrategy::saveUser(const std::shared_ptr<User>& user) {
    auto logger = Logger::getInstance();
    logger->info("正在保存用户信息到文件存储: " + user->getUsername());

    string filePath = dataPath + "/users.txt";

    // 先读取所有用户
    ifstream       infile(filePath);
    string         line;
    vector<string> lines;
    bool           userFound = false;

    if (infile.is_open()) {
        while (getline(infile, line)) {
            stringstream ss(line);
            string       idStr;
            getline(ss, idStr, ',');

            // 检查是否为当前用户的行
            if (!idStr.empty() && stoi(idStr) == user->getUserId()) {
                // 替换为新的用户信息
                userFound = true;
                lines.push_back(to_string(user->getUserId()) + "," + user->getUsername() + "," + user->getPasswordHash() + "," + user->getUserType());
            } else {
                // 保留其他用户的行
                lines.push_back(line);
            }
        }
        infile.close();
    } else {
        logger->warning("无法打开用户文件进行读取：" + filePath + "，将创建新文件");
    }

    // 如果没有找到用户，添加新用户
    if (!userFound) {
        lines.push_back(to_string(user->getUserId()) + "," + user->getUsername() + "," + user->getPasswordHash() + "," + user->getUserType());
    }

    // 写回所有用户
    ofstream outfile(filePath);
    if (!outfile.is_open()) {
        logger->mistake("无法打开用户文件进行写入：" + filePath);
        return false;
    }

    for (const auto& updatedLine : lines) {
        outfile << updatedLine << "\n";
    }
    outfile.close();

    logger->info("成功"s + (userFound ? "更新" : "添加") + "用户信息: " + user->getUsername() +
                 " (ID: " + to_string(user->getUserId()) + ", 类型: " + user->getUserType() + ")");

    return true;
}
std::shared_ptr<User> FileStorageStrategy::loadUser(const std::string& username) {
    auto logger = Logger::getInstance();
    logger->info("正在从文件存储加载用户信息: " + username);

    string   filePath = dataPath + "/users.txt";
    ifstream infile(filePath);
    if (!infile.is_open()) {
        logger->mistake("无法打开用户文件：" + filePath);
        return nullptr;
    }

    string line;
    while (getline(infile, line)) {
        stringstream   ss(line);
        string         token;
        vector<string> tokens;
        while (getline(ss, token, ',')) {
            tokens.push_back(token);
        }
        if (tokens.size() == 4 && tokens[1] == username) {
            int    userId       = stoi(tokens[0]);
            string username     = tokens[1];
            string passwordHash = tokens[2];
            string userType     = tokens[3];

            logger->info("找到用户: " + username + " (ID: " + to_string(userId) + ", 类型: " + userType + ")");

            if (userType == "admin") {
                return make_shared<AdminUser>(userId, username, passwordHash);
            } else if (userType == "regular") {
                return make_shared<RegularUser>(userId, username, passwordHash);
            }
        }
    }
    infile.close();

    logger->info("用户 " + username + " 不存在");
    return nullptr;
}

vector<shared_ptr<User>> FileStorageStrategy::loadAllUsers() {
    auto logger = Logger::getInstance();
    logger->info("正在从文件存储加载所有用户信息");

    vector<shared_ptr<User>> users;
    string                   filePath = dataPath + "/users.txt";  // 文件名称
    ifstream                 infile(filePath);
    if (!infile.is_open()) {
        logger->mistake("无法打开用户文件: " + filePath);
        return users;  // 文件打开失败, 返回空用户列表
    }

    string line;
    int    adminCount   = 0;
    int    regularCount = 0;

    while (getline(infile, line)) {
        stringstream   ss(line);
        string         token;
        vector<string> tokens;
        while (getline(ss, token, ',')) {
            tokens.push_back(token);  // 将每行数据按逗号分隔存入tokens中
        }
        if (tokens.size() == 4) {
            int    userId       = stoi(tokens[0]);
            string username     = tokens[1];
            string passwordHash = tokens[2];
            string userType     = tokens[3];  // 根据用户类型创建不同的User对象
            if (userType == "admin") {
                users.push_back(make_shared<AdminUser>(userId, username, passwordHash));
                adminCount++;
            } else if (userType == "regular") {
                users.push_back(make_shared<RegularUser>(userId, username, passwordHash));
                regularCount++;
            }
        } else {
            logger->warning("跳过格式不正确的用户记录");
        }
    }
    infile.close();

    logger->info("成功加载 " + to_string(users.size()) + " 位用户 (管理员: " + to_string(adminCount) + ", 普通用户: " + to_string(regularCount) + ")");
    return users;
}

bool FileStorageStrategy::validateCredentials(const std::string& username, const std::string& passwordHash) {
    auto logger = Logger::getInstance();
    logger->info("正在验证用户凭据: " + username);

    string   filePath = dataPath + "/users.txt";  // 文件名称
    ifstream infile(filePath);
    if (!infile.is_open()) {
        logger->mistake("无法打开用户文件: " + filePath);
        return false;  // 文件打开失败
    }

    string line;
    while (getline(infile, line)) {
        stringstream   ss(line);
        string         token;
        vector<string> tokens;
        while (getline(ss, token, ',')) {
            tokens.push_back(token);  // 将每行数据按逗号分隔存入tokens中
        }
        if (tokens.size() == 4 && tokens[1] == username && tokens[2] == passwordHash) {
            infile.close();
            logger->info("用户 " + username + " 凭据验证成功");
            return true;  // 用户名和密码匹配
        }
    }
    infile.close();

    logger->warning("用户 " + username + " 凭据验证失败");
    return false;  // 用户名或密码不匹配
}
bool FileStorageStrategy::deleteUser(int userId) {
    auto logger = Logger::getInstance();
    logger->info("正在从文件存储中删除用户ID: " + std::to_string(userId));

    std::string   filePath = dataPath + "/users.txt";
    std::ifstream infile(filePath);
    if (!infile.is_open()) {
        logger->mistake("无法打开用户文件: " + filePath);
        return false;
    }

    std::vector<std::string> lines;
    std::string              line;
    bool                     found = false;
    while (std::getline(infile, line)) {
        std::stringstream ss(line);
        std::string       token;
        std::getline(ss, token, ',');
        if (std::stoi(token) == userId) {
            found = true;
            logger->info("找到用户ID " + std::to_string(userId) + "，已标记为删除");
            continue;  // 跳过该用户
        }
        lines.push_back(line);
    }
    infile.close();

    std::ofstream outfile(filePath, std::ios::trunc);
    if (!outfile.is_open()) {
        logger->mistake("无法写入用户文件: " + filePath);
        return false;
    }

    for (const auto& l : lines)
        outfile << l << "\n";

    if (found) {
        logger->info("用户ID " + std::to_string(userId) + " 已成功从文件存储中删除");
    } else {
        logger->warning("用户ID " + std::to_string(userId) + " 未找到，无法删除");
    }

    return found;
}

//------------- 数据库存储策略类 ---------------
MySQLStorageStrategy::MySQLStorageStrategy(const string& host,
                                           const string& user,
                                           const string& password,
                                           const string& database,
                                           int           port)
    : host(host), user(user), password(password), database(database), port(port) {
    connect();
}
bool MySQLStorageStrategy::connect() {
    auto        logger       = Logger::getInstance();
    static bool firstConnect = true;
    try {
        // 仅第一次显示连接信息，减少冗余日志
        if (firstConnect) {
            logger->info("正在连接到MySQL数据库");
            firstConnect = false;
        }
        // 初始化数据库连接
        mysql = mysql_init(nullptr);
        if (mysql == nullptr) {
            logger->mistake("无法初始化MySQL数据库连接");
            return false;
        }
        // 连接到数据库
        if (!reconnectDatabase()) {
            logger->mistake("无法连接到MySQL数据库");
            return false;
        }
        mysql_set_character_set(mysql, "utf8mb4");

        return true;
    } catch (const std::exception& e) {
        logger->mistake("MySQL数据库连接失败: " + string(e.what()));
        if (mysql) {
            mysql_close(mysql);
            mysql = nullptr;
        }
        return false;
    }
}

void MySQLStorageStrategy::disconnect() {
    if (mysql) {
        mysql_close(mysql);
        mysql = nullptr;
        // 移除直接输出到控制台的消息，由Logger记录即可
    }
}

// 转义SQL字符串，防止SQL注入
std::string MySQLStorageStrategy::escapeString(const std::string& str) {
    if (!mysql || str.empty()) {
        return str;  // 如果无法连接或字符串为空，返回原字符串
    }

    char* escaped = new char[str.length() * 2 + 1];
    mysql_real_escape_string(mysql, escaped, str.c_str(), str.length());
    std::string result(escaped);
    delete[] escaped;
    return result;
}
bool MySQLStorageStrategy::reconnectDatabase() {
    auto logger = Logger::getInstance();
    if (mysql != nullptr) {
        // 检查连接是否有效
        if (mysql_ping(mysql) == 0) {
            return true;  // 连接有效
        }

        // 连接失效，关闭旧连接
        mysql_close(mysql);
        mysql = mysql_init(nullptr);
        if (mysql == nullptr) {
            return false;
        }
    }

    // 建立新连接
    if (!mysql_real_connect(mysql, host.c_str(), user.c_str(), password.c_str(),
                            database.c_str(), port, nullptr, 0)) {
        return false;
    }

    // 设置字符集
    mysql_set_character_set(mysql, "utf8mb4");
    // 使用静态变量记录连接次数，减少重复日志
    static int reconnectCount = 0;
    if (reconnectCount++ < 2) {  // 只显示前两次重连信息
        logger->info("MySQL数据库连接" + std::string(reconnectCount > 1 ? "成功" : "已初始化"));
    }
    return true;
}
// 保存好友信息
bool MySQLStorageStrategy::saveFriends(const map<string, Friend>& friendMap, int userId) {
    auto logger = Logger::getInstance();

    // 验证这是一个有效的用户ID
    if (userId <= 0) {
        logger->warning("尝试保存无效用户ID的联系人数据: " + to_string(userId));
        return false;
    }

    // 验证用户ID是否在数据库中存在
    if (!connect()) {
        logger->mistake("数据库连接失败，无法验证用户");
        return false;
    }

    string checkUserQuery = "SELECT id FROM users WHERE id = " + to_string(userId);
    if (mysql_query(mysql, checkUserQuery.c_str())) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("验证用户ID失败: " + mistake_msg);
        disconnect();
        return false;
    }

    MYSQL_RES* result = mysql_store_result(mysql);
    if (!result || mysql_num_rows(result) == 0) {
        logger->warning("找不到ID为 " + to_string(userId) + " 的用户，无法保存联系人数据");
        if (result)
            mysql_free_result(result);
        disconnect();
        return false;
    }
    mysql_free_result(result);

    logger->info("正在保存用户ID " + to_string(userId) + " 的好友信息到MySQL数据库");

    // 删除旧的联系人数据
    string deleteQuery = "DELETE FROM friends WHERE user_id = " + to_string(userId);
    if (mysql_query(mysql, deleteQuery.c_str())) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("删除旧好友数据失败: " + mistake_msg);
        disconnect();
        return false;
    }

    int loseCount = 0;
    for (const auto& pair : friendMap) {
        const Friend& friendInfo = pair.second;

        // 转义特殊字符以防止SQL注入
        string name     = escapeString(friendInfo.getName());
        string phone    = escapeString(friendInfo.getPhoneNumber());
        string address  = escapeString(friendInfo.getAddress());
        string birthday = escapeString(friendInfo.getBirthday());
        string email    = escapeString(friendInfo.getEmail());
        string note     = escapeString(friendInfo.getNote());

        // 转义所有字段的单引号，防止SQL注入
        auto escapeSQL = [](std::string& str) {
            size_t pos = 0;
            while((pos = str.find("'", pos)) != std::string::npos) {
                str.replace(pos, 1, "''");
                pos += 2;
            }
        };
        
        std::string nameSafe = name;
        std::string phoneSafe = phone;
        std::string addressSafe = address;
        std::string birthdaySafe = birthday;
        std::string emailSafe = email;
        std::string noteSafe = note;
        
        escapeSQL(nameSafe);
        escapeSQL(phoneSafe);
        escapeSQL(addressSafe);
        escapeSQL(birthdaySafe);
        escapeSQL(emailSafe);
        escapeSQL(noteSafe);
        
        string insertQuery = "INSERT INTO friends (user_id, name, phone_number, address, birthday, email, note) VALUES (" +
                             to_string(userId) + ", '" + nameSafe + "', '" + phoneSafe + "', '" +
                             addressSafe + "', '" + birthdaySafe + "', '" +
                             emailSafe + "', '" + noteSafe + "')";
        if (mysql_query(mysql, insertQuery.c_str())) {
            string mistake_msg = mysql_error(mysql);
            logger->mistake("插入好友数据失败: " + mistake_msg + ", 联系人: " + friendInfo.getName());
            loseCount++;
            // 继续尝试插入其他联系人，而不是立即返回
        }
    }
    int successCount = friendMap.size() - loseCount;

    disconnect();

    if (loseCount > 0) {
        logger->warning("部分保存用户ID " + to_string(userId) + " 的联系人：成功 " + to_string(successCount) +
                        " 位，失败 " + to_string(loseCount) + " 位");
    } else {
        logger->info("成功保存用户ID " + to_string(userId) + " 的所有 " + to_string(successCount) + " 位联系人");
    }

    // 只有当所有联系人都保存失败时返回false
    return successCount > 0;
}
// 加载好友信息
bool MySQLStorageStrategy::loadFriends(map<string, Friend>& friendMap, int userId) {
    auto logger = Logger::getInstance();
    logger->info("正在从MySQL数据库加载用户ID " + to_string(userId) + " 的好友信息");

    if (!connect()) {
        logger->mistake("数据库连接失败，无法加载好友信息");
        return false;  // 数据库未连接
    }

    string query = "SELECT name, phone_number, address, email, birthday, note FROM friends WHERE user_id = " + to_string(userId);
    if (mysql_query(mysql, query.c_str())) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("查询好友信息失败: " + mistake_msg);
        disconnect();  // 清理数据库连接
        return false;  // 查询失败
    }

    MYSQL_RES* result = mysql_store_result(mysql);
    if (!result) {
        logger->mistake("获取查询结果集失败");
        disconnect();  // 清理数据库连接
        return false;  // 查询结果为空
    }

    friendMap.clear();  // 清空现有好友列表
    MYSQL_ROW row;
    int       friendCount = 0;

    while ((row = mysql_fetch_row(result))) {
        std::string name     = row[0];
        std::string phone    = row[1];
        std::string address  = row[2];
        std::string email    = row[3];
        std::string birthday = row[4];
        std::string note     = row[5];

        Friend friend_(name, phone, address, birthday, email, note);
        friendMap[phone] = friend_;  // 将查询结果存入friendMap中
        friendCount++;
    }

    mysql_free_result(result);
    disconnect();

    logger->info("成功从数据库加载用户ID " + to_string(userId) + " 的 " + to_string(friendCount) + " 位好友信息");
    return true;
}

bool MySQLStorageStrategy::saveUser(const shared_ptr<User>& user) {
    auto logger = Logger::getInstance();
    logger->info("正在保存用户信息到MySQL数据库: " + user->getUsername());

    if (!connect()) {
        logger->mistake("数据库连接失败，无法保存用户信息");
        return false;
    }

    // 转义用户数据中的单引号，防止SQL注入
    auto escapeSQL = [](std::string str) {
        size_t pos = 0;
        while((pos = str.find("'", pos)) != std::string::npos) {
            str.replace(pos, 1, "''");
            pos += 2;
        }
        return str;
    };
    
    std::string usernameSafe = escapeSQL(user->getUsername());
    std::string passwordHashSafe = escapeSQL(user->getPasswordHash());
    std::string userTypeSafe = escapeSQL(user->getUserType());
    
    string query;
    bool result = false;
    
    // 检查是新用户创建还是现有用户更新
    if (user->getUserId() == -1) {
        // 新用户插入
        query = "INSERT INTO users (username, password_hash, user_type) VALUES ('" + 
                usernameSafe + "', '" + 
                passwordHashSafe + "', '" + 
                userTypeSafe + "');";
                
        logger->info("创建新用户: " + user->getUsername() + ", 类型: " + user->getUserType());
    } else {
        // 现有用户更新
        query = std::string("UPDATE users SET ") +
                "username = '" + usernameSafe + "', " +
                "password_hash = '" + passwordHashSafe + "', " +
                "user_type = '" + userTypeSafe + "' " +
                "WHERE id = " + to_string(user->getUserId());
                
        logger->info("更新现有用户: " + user->getUsername() + ", ID: " + to_string(user->getUserId()));
    }

    result = (mysql_query(mysql, query.c_str()) == 0);
    if (!result) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("保存用户信息失败: " + mistake_msg);
    } else {
        // 如果是新用户，获取自动生成的ID
        if (user->getUserId() == -1) {
            logger->info("新用户创建成功，获取自动生成的ID");
        } else {
            logger->info("成功更新用户信息: " + user->getUsername() + " (ID: " + to_string(user->getUserId()) + ", 类型: " + user->getUserType() + ")");
        }
    }

    disconnect();
    return result;
}

shared_ptr<User> MySQLStorageStrategy::loadUser(const string& username) {
    auto logger = Logger::getInstance();
    logger->info("正在从MySQL数据库加载用户信息: " + username);

    if (!connect()) {
        logger->mistake("数据库连接失败，无法加载用户信息");
        return nullptr;  // 数据库未连接
    }

    string query = "SELECT id, username, password_hash, user_type FROM users WHERE username = '" + username + "'";  // 查询用户信息
    if (mysql_query(mysql, query.c_str())) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("查询用户信息失败: " + mistake_msg);
        disconnect();    // 清理数据库连接
        return nullptr;  // 查询失败
    }

    MYSQL_RES* result = mysql_store_result(mysql);
    if (!result) {
        logger->mistake("获取查询结果集失败");
        disconnect();    // 清理数据库连接
        return nullptr;  // 查询结果为空
    }

    MYSQL_ROW row = mysql_fetch_row(result);
    if (!row) {
        logger->warning("用户不存在: " + username);
        mysql_free_result(result);
        disconnect();    // 清理数据库连接
        return nullptr;  // 用户不存在
    }

    int    userId       = atoi(row[0]);
    string name         = row[1];
    string passwordHash = row[2];
    string userType     = row[3];

    logger->info("找到用户: " + name + " (ID: " + to_string(userId) + ", 类型: " + userType + ")");
    shared_ptr<User> user = nullptr;

    if (userType == "admin") {
        user = make_shared<AdminUser>(userId, name, passwordHash);
    } else if (userType == "regular") {
        user = make_shared<RegularUser>(userId, name, passwordHash);
    } else {
        logger->warning("未知的用户类型: " + userType + ", 用户: " + username);
    }

    mysql_free_result(result);
    disconnect();  // 清理数据库连接
    return user;
}
vector<shared_ptr<User>> MySQLStorageStrategy::loadAllUsers() {
    auto logger = Logger::getInstance();
    logger->info("正在从MySQL数据库加载所有用户信息");

    vector<shared_ptr<User>> users;
    if (!connect()) {
        logger->mistake("数据库连接失败，无法加载用户信息");
        return users;  // 数据库未连接, 返回空用户列表
    }

    string query = "SELECT id, username, password_hash, user_type FROM users";
    if (mysql_query(mysql, query.c_str())) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("查询所有用户信息失败: " + mistake_msg);
        disconnect();  // 清理数据库连接
        return users;  // 查询失败，返回空用户列表
    }

    MYSQL_RES* result = mysql_store_result(mysql);
    if (!result) {
        logger->mistake("获取查询结果集失败");
        disconnect();  // 清理数据库连接
        return users;  // 查询结果为空，返回空用户列表
    }

    MYSQL_ROW row;
    int       adminCount   = 0;
    int       regularCount = 0;
    int       unknownCount = 0;

    while ((row = mysql_fetch_row(result))) {
        int    userId       = atoi(row[0]);
        string name         = row[1] ? row[1] : "";
        string passwordHash = row[2] ? row[2] : "";
        string userType     = row[3] ? row[3] : "";

        if (userType == "admin") {
            users.push_back(make_shared<AdminUser>(userId, name, passwordHash));
            adminCount++;
        } else if (userType == "regular") {
            users.push_back(make_shared<RegularUser>(userId, name, passwordHash));
            regularCount++;
        } else {
            logger->warning("跳过未知类型的用户: " + name + ", 类型: " + userType);
            unknownCount++;
        }
    }

    mysql_free_result(result);
    disconnect();

    logger->info("成功加载 " + to_string(users.size()) + " 位用户 (管理员: " + to_string(adminCount) +
                 ", 普通用户: " + to_string(regularCount) +
                 (unknownCount > 0 ? ", 跳过未知类型用户: " + to_string(unknownCount) : "") + ")");
    return users;
}
bool MySQLStorageStrategy::deleteUser(int userId) {
    auto logger = Logger::getInstance();
    logger->info("正在从MySQL数据库中删除用户ID: " + std::to_string(userId));

    if (!connect()) {
        logger->mistake("数据库连接失败，无法删除用户ID: " + std::to_string(userId));
        return false;
    }

    std::string query  = "DELETE FROM users WHERE id = " + std::to_string(userId);
    bool        result = (mysql_query(mysql, query.c_str()) == 0);

    if (!result) {
        std::string mistake_msg = mysql_error(mysql);
        logger->mistake("删除用户失败: " + mistake_msg);
    } else {
        int affected_rows = (int)mysql_affected_rows(mysql);
        if (affected_rows > 0) {
            logger->info("用户ID " + std::to_string(userId) + " 已成功从数据库中删除");
        } else {
            logger->warning("用户ID " + std::to_string(userId) + " 在数据库中未找到，无法删除");
            result = false;
        }
    }

    disconnect();
    return result;
}
bool MySQLStorageStrategy::validateCredentials(const string& username, const string& passwordHash) {
    auto logger = Logger::getInstance();
    logger->info("正在验证用户凭据: " + username);

    if (!connect()) {
        logger->mistake("数据库连接失败，无法验证用户凭据");
        return false;
    }

    string query = "SELECT id FROM users WHERE username = '" + username + "' AND password_hash = '" + passwordHash + "'";
    if (mysql_query(mysql, query.c_str())) {
        string mistake_msg = mysql_error(mysql);
        logger->mistake("验证用户凭据查询失败: " + mistake_msg);
        disconnect();
        return false;
    }

    MYSQL_RES* result = mysql_store_result(mysql);
    if (!result) {
        logger->mistake("获取查询结果集失败");
        disconnect();
        return false;
    }

    MYSQL_ROW row     = mysql_fetch_row(result);
    bool      isValid = (row != nullptr);

    if (isValid) {
        logger->info("用户 " + username + " 凭据验证成功");
    } else {
        logger->warning("用户 " + username + " 凭据验证失败");
    }

    mysql_free_result(result);
    disconnect();
    return isValid;
}

// -----------------数据库管理器------------------
DatabaseManager::DatabaseManager(shared_ptr<StorageStrategy> strategy) : strategy(strategy) {}
// 设置存储策略
void DatabaseManager::setStrategy(shared_ptr<StorageStrategy> strategy) {
    this->strategy = strategy;
}
// 保存好友信息
bool DatabaseManager::saveFriends(const map<string, Friend>& friendMap, int userId) {
    auto logger = Logger::getInstance();
    logger->info("DatabaseManager: 正在保存用户ID " + to_string(userId) + " 的好友信息");

    bool result = strategy->saveFriends(friendMap, userId);

    if (result) {
        logger->info("DatabaseManager: 用户ID " + to_string(userId) + " 的好友信息保存成功");
    } else {
        logger->mistake("DatabaseManager: 用户ID " + to_string(userId) + " 的好友信息保存失败");
    }

    return result;
}

// 加载好友信息
bool DatabaseManager::loadFriends(map<string, Friend>& friendMap, int userId) {
    auto logger = Logger::getInstance();
    logger->info("DatabaseManager: 正在加载用户ID " + to_string(userId) + " 的好友信息");

    bool result = strategy->loadFriends(friendMap, userId);

    if (result) {
        logger->info("DatabaseManager: 用户ID " + to_string(userId) + " 的好友信息加载成功，共 " + to_string(friendMap.size()) + " 位联系人");
    } else {
        logger->warning("DatabaseManager: 用户ID " + to_string(userId) + " 的好友信息加载失败");
    }

    return result;
}
// 保存用户信息
bool DatabaseManager::saveUser(const shared_ptr<User>& user) {
    auto logger = Logger::getInstance();
    logger->info("DatabaseManager: 正在保存用户信息: " + user->getUsername());

    bool result = strategy->saveUser(user);

    if (result) {
        logger->info("DatabaseManager: 用户信息保存成功: " + user->getUsername());
    } else {
        logger->mistake("DatabaseManager: 用户信息保存失败: " + user->getUsername());
    }

    return result;
}

// 加载用户数据
shared_ptr<User> DatabaseManager::loadUser(const string& username) {
    auto logger = Logger::getInstance();
    logger->info("DatabaseManager: 正在加载用户数据: " + username);

    shared_ptr<User> user = strategy->loadUser(username);

    if (user) {
        logger->info("DatabaseManager: 用户数据加载成功: " + username);
    } else {
        logger->warning("DatabaseManager: 用户数据加载失败: " + username);
    }

    return user;
}

// 加载所有用户数据
vector<shared_ptr<User>> DatabaseManager::loadAllUsers() {
    auto logger = Logger::getInstance();
    logger->info("DatabaseManager: 正在加载所有用户数据");

    vector<shared_ptr<User>> users = strategy->loadAllUsers();

    logger->info("DatabaseManager: 成功加载 " + to_string(users.size()) + " 位用户");
    return users;
}
bool DatabaseManager::deleteUser(int userId) {
    auto logger = Logger::getInstance();
    logger->info("正在请求删除用户ID: " + std::to_string(userId));

    bool result = strategy->deleteUser(userId);

    if (result) {
        logger->info("用户ID " + std::to_string(userId) + " 删除操作成功完成");
    } else {
        logger->warning("用户ID " + std::to_string(userId) + " 删除操作失败");
    }

    return result;
}

// 验证用户凭据
bool DatabaseManager::validateCredentials(const string& username, const string& passwordHash) {
    auto logger = Logger::getInstance();
    logger->info("DatabaseManager: 正在验证用户凭据: " + username);
    return strategy->validateCredentials(username, passwordHash);
}