{"configurations": [{"name": "MinGW64", "includePath": ["${workspaceFolder}/**", "F:/msys64/mingw64/include/**", "F:/MySQL/MySQL Server 5.7/include", "F:/MySQL/MySQL Server 5.7/include/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "compilerPath": "F:/msys64/mingw64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64", "browse": {"path": ["${workspaceFolder}/**", "F:/MySQL/MySQL Server 5.7/include/**"], "limitSymbolsToIncludedHeaders": true}}], "version": 4}