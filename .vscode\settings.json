{
    "files.associations": {
        "*.dbclient-js": "javascript",
        "*.h": "cpp",
        "*.c": "c",
        "*.hpp": "cpp",
        "*.cpp": "cpp",
        ".clang-format": "yaml",
        "unordered_map": "cpp",
        "iostream": "cpp",
        "*.tcc": "cpp",
        "ostream": "cpp",
        "sstream": "cpp",
        "array": "cpp",
        "atomic": "cpp",
        "bit": "cpp",
        "bitset": "cpp",
        "cctype": "cpp",
        "charconv": "cpp",
        "chrono": "cpp",
        "clocale": "cpp",
        "cmath": "cpp",
        "compare": "cpp",
        "concepts": "cpp",
        "condition_variable": "cpp",
        "cstdarg": "cpp",
        "cstddef": "cpp",
        "cstdint": "cpp",
        "cstdio": "cpp",
        "cstdlib": "cpp",
        "cstring": "cpp",
        "ctime": "cpp",
        "cwchar": "cpp",
        "cwctype": "cpp",
        "deque": "cpp",
        "list": "cpp",
        "map": "cpp",
        "set": "cpp",
        "string": "cpp",
        "vector": "cpp",
        "exception": "cpp",
        "algorithm": "cpp",
        "functional": "cpp",
        "iterator": "cpp",
        "memory": "cpp",
        "memory_resource": "cpp",
        "numeric": "cpp",
        "optional": "cpp",
        "random": "cpp",
        "ratio": "cpp",
        "string_view": "cpp",
        "system_error": "cpp",
        "tuple": "cpp",
        "type_traits": "cpp",
        "utility": "cpp",
        "format": "cpp",
        "initializer_list": "cpp",
        "iomanip": "cpp",
        "iosfwd": "cpp",
        "istream": "cpp",
        "limits": "cpp",
        "mutex": "cpp",
        "new": "cpp",
        "numbers": "cpp",
        "ranges": "cpp",
        "semaphore": "cpp",
        "shared_mutex": "cpp",
        "span": "cpp",
        "stdexcept": "cpp",
        "stop_token": "cpp",
        "streambuf": "cpp",
        "text_encoding": "cpp",
        "thread": "cpp",
        "cinttypes": "cpp",
        "typeinfo": "cpp",
        "variant": "cpp",
        "fstream": "cpp",
        "codecvt": "cpp",
        "*.inc": "cpp",
        "print": "cpp"
    },
    "Codegeex.RepoIndex": true,
    "C_Cpp.default.includePath": [
        "F:/MySQL/MySQL Server 5.7/include**",
        "F:/C++project/myContact/include/**"
    ],
    "C_Cpp.intelliSenseEngine": "default",
    "C_Cpp.errorSquiggles": "enabled",
    "C_Cpp.autocomplete": "default",
    "C_Cpp.default.compilerPath": "F:/msys64/mingw64/bin/g++.exe",
    "C_Cpp.clang_format_path": "C:/Users/<USER>/.vscode/extensions/ms-vscode.cpptools-1.25.3-win32-x64/LLVM/bin/clang-format.exe",
    "C_Cpp.formatting": "clangFormat",
    "C_Cpp.clang_format_fallbackStyle": "Visual Studio",
    "C_Cpp.clang_format_sortIncludes": null,
    "C_Cpp.clang_format_style": "file",
}