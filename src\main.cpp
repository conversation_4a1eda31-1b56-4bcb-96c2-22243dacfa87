
#include <iostream>
#include <memory>
#include <string>
#include "../include/ContactManager.h"
#include "../include/DatabaseManager.h"
#include "../include/Logger.h"
#include "../include/MenuSystem.h"
#include "../include/UserManager.h"

// 数据库连接信息
const std::string DB_HOST     = "localhost";
const std::string DB_USER     = "root";        // 默认MySQL用户名，请根据实际情况修改
const std::string DB_PASSWORD = "78963.";      // 默认密码，请根据实际情况修改
const std::string DB_NAME     = "contact_db";  // 数据库名称
const int         DB_PORT     = 3306;          // 默认MySQL端口

// 数据存储目录
const std::string DATA_PATH = "f:/Project/C++project/myContact/data/";

int main() {
    try {
        // 设置控制台输出编码为UTF-8
        SetConsoleOutputCP(CP_UTF8);

        // 启用ANSI颜色支持
        HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
        DWORD dwMode = 0;
        GetConsoleMode(hOut, &dwMode);
        dwMode |= ENABLE_VIRTUAL_TERMINAL_PROCESSING;
        SetConsoleMode(hOut, dwMode);

        // 初始化日志系统
        auto logger = Logger::getInstance();
        logger->info("系统启动中...");

        // 创建数据库存储策略（可以选择MySQL或文件存储）
        std::shared_ptr<StorageStrategy> storageStrategy;

        try {
            // 首先尝试使用MySQL存储
            storageStrategy = std::make_shared<MySQLStorageStrategy>(
                DB_HOST, DB_USER, DB_PASSWORD, DB_NAME, DB_PORT);
            logger->info("已连接到MySQL数据库");
        } catch (const std::exception& e) {
            logger->warning("MySQL连接失败: " + std::string(e.what()) + "，将使用文件存储");
            // 如果MySQL连接失败，回退到文件存储
            storageStrategy = std::make_shared<FileStorageStrategy>(DATA_PATH);
            logger->info("已启用文件存储模式");
        }

        // 创建数据库管理器
        auto dbManager = std::make_shared<DatabaseManager>(storageStrategy);

        // 创建用户管理器
        auto userManager = std::make_shared<UserManager>(dbManager);

        // 创建联系人管理器 - ContactManager只接受DatabaseManager参数
        auto contactManager = std::make_shared<ContactManager>(dbManager);

        // 创建菜单系统
        MenuSystem menuSystem(userManager, contactManager, logger);

        logger->info("系统初始化完成");

        // 运行菜单系统
        menuSystem.run();

        logger->info("系统正常退出");
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "系统发生严重错误: " << e.what() << std::endl;
        auto logger = Logger::getInstance();
        logger->mistake("系统崩溃: " + std::string(e.what()));
        return 1;
    }
}
