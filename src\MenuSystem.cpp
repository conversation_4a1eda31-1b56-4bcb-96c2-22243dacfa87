#include "../include/MenuSystem.h"
#include "../include/ContactManager.h"
#include "../include/Friend.h"
#include "../include/Logger.h"
#include "../include/User.h"
#include "../include/UserManager.h"

#include <cstdlib>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <limits>
#include <sstream>

#ifdef _WIN32
#include <conio.h>  // 用于Windows下的getch()
#include <windows.h>
#else
#include <termios.h>
#include <unistd.h>
#endif

// ===== MenuItem 实现 =====
MenuItem::MenuItem(const std::string& name, const std::string& description, std::function<void()> action)
    : name(name), description(description), action(action) {}

std::string MenuItem::getName() const {
    return name;
}

std::string MenuItem::getDescription() const {
    return description;
}

void MenuItem::execute() const {
    action();
}

// ===== Menu 实现 =====
Menu::Menu(const std::string& title, bool exitOption)
    : title(title), exitOption(exitOption) {}

void Menu::addItem(const MenuItem& item) {
    items.push_back(item);
}

void Menu::display() const {
    // 简化的菜单显示
    std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;
    std::cout << "\033[1;33m          " << title << "\033[0m" << std::endl;
    std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;

    for (size_t i = 0; i < items.size(); ++i) {
        std::cout << "\033[1;32m" << i + 1 << "\033[0m. "
                  << std::setw(12) << std::left << items[i].getName()
                  << "- " << items[i].getDescription() << std::endl;
    }

    if (exitOption) {
        std::cout << "\033[1;31m0\033[0m. "
                  << std::setw(12) << std::left << "退出"
                  << "- 退出系统" << std::endl;
    }

    std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;
    std::cout << "请选择操作 [0-" << items.size() << "]: ";
}

bool Menu::handleChoice() {
    auto logger = Logger::getInstance();
    int  choice;
    std::cin >> choice;

    // 清除输入缓冲区
    std::cin.clear();
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');

    if (choice == 0 && exitOption) {
        return false;
    }

    if (choice > 0 && choice <= static_cast<int>(items.size())) {
        items[choice - 1].execute();
    } else {
        logger->warning("用户选择了无效选项", "System");
        std::cout << "无效的选择，请重试。" << std::endl;
    }

    return true;
}

// ===== MenuSystem 实现 =====
MenuSystem::MenuSystem(std::shared_ptr<UserManager>    userManager,
                       std::shared_ptr<ContactManager> contactManager,
                       std::shared_ptr<Logger>         logger)
    : userManager(userManager), contactManager(contactManager), logger(logger), currentMenu(nullptr) {
    initMainMenu();
    initLoginMenu();
    initContactMenu();
    initAdminMenu();
    initUserMenu();

    // 默认显示主菜单
    currentMenu = mainMenu.get();
}

MenuSystem::~MenuSystem() {
    // 智能指针会自动释放内存，无需手动清理
}

void MenuSystem::clearScreen() const {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void MenuSystem::pauseScreen() const {
    std::cout << "\033[1;90m按任意键继续...\033[0m" << std::flush;
#ifdef _WIN32
    _getch();
#else
    // Linux/macOS上的替代方案
    struct termios old_tio, new_tio;
    tcgetattr(STDIN_FILENO, &old_tio);
    new_tio = old_tio;
    new_tio.c_lflag &= (~ICANON & ~ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &new_tio);
    getchar();
    tcsetattr(STDIN_FILENO, TCSANOW, &old_tio);
#endif
    // 按键后清除提示文字行
    std::cout << "\r                           \r" << std::flush;
}

std::string MenuSystem::getPasswordInput(const std::string& prompt) const {
    std::string password;
    char        ch;

    std::cout << prompt;

#ifdef _WIN32
    while ((ch = _getch()) != '\r') {
        if (ch == '\b') {  // 退格键
            if (!password.empty()) {
                password.pop_back();
                std::cout << "\b \b";  // 清除上一个字符
            }
        } else {
            password.push_back(ch);
            std::cout << '*';  // 显示星号
        }
    }
#else
    struct termios old_tio, new_tio;
    tcgetattr(STDIN_FILENO, &old_tio);
    new_tio = old_tio;
    new_tio.c_lflag &= (~ICANON & ~ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &new_tio);

    while ((ch = getchar()) != '\n') {
        if (ch == 127) {  // 退格键
            if (!password.empty()) {
                password.pop_back();
                std::cout << "\b \b";  // 清除上一个字符
            }
        } else {
            password.push_back(ch);
            std::cout << '*';  // 显示星号
        }
    }

    tcsetattr(STDIN_FILENO, TCSANOW, &old_tio);
#endif

    std::cout << std::endl;
    return password;
}

void MenuSystem::displayHeader(const std::string& title) const {
    clearScreen();
    // 简化的标题显示
    std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;
    std::cout << "\033[1;33m          " << title << "\033[0m" << std::endl;
    std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;
    std::cout << std::endl;
}

void MenuSystem::displayFooter() const {
    std::cout << std::endl;
    std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;
    std::cout << "\033[1;90m© " << __DATE__ << " 联系人管理系统\033[0m" << std::endl;
}

// 显示美化的联系人列表标题
void MenuSystem::displayContactTableHeader() const {
    // 简化的表格标题
    std::cout << std::left
              << std::setw(20) << "姓名"
              << std::setw(15) << "电话"
              << std::setw(30) << "地址"
              << std::setw(15) << "生日"
              << std::setw(25) << "邮箱"
              << "备注" << std::endl;
    std::cout << std::string(110, '-') << std::endl;
}

void MenuSystem::initMainMenu() {
    mainMenu = std::make_unique<Menu>("联系人管理系统");
    mainMenu->addItem(MenuItem("登录", "登录到系统", [this]() { switchToMenu("login"); }));
    mainMenu->addItem(MenuItem("注册", "创建新账户", [this]() { registerUser(); }));
    mainMenu->addItem(MenuItem("帮助", "显示使用帮助", [this]() { showHelp(); }));
    mainMenu->addItem(MenuItem("退出", "退出系统", [this]() { exit(); }));
}

void MenuSystem::initLoginMenu() {
    loginMenu = std::make_unique<Menu>("用户登录");
    loginMenu->addItem(MenuItem("登录", "输入用户名和密码", [this]() { login(); }));
    loginMenu->addItem(MenuItem("返回", "返回主菜单", [this]() { switchToMenu("main"); }));
}

void MenuSystem::initContactMenu() {
    contactMenu = std::make_unique<Menu>("联系人管理");
    contactMenu->addItem(MenuItem("查看所有联系人", "显示所有联系人信息", [this]() { viewContacts(); }));
    contactMenu->addItem(MenuItem("添加联系人", "添加新联系人", [this]() { addContact(); }));
    contactMenu->addItem(MenuItem("删除联系人", "删除现有联系人", [this]() { deleteContact(); }));
    contactMenu->addItem(MenuItem("更新联系人", "更新现有联系人信息", [this]() { updateContact(); }));
    contactMenu->addItem(MenuItem("搜索联系人", "按姓名或电话号码搜索联系人", [this]() { searchContact(); }));
    contactMenu->addItem(MenuItem("导出联系人", "导出联系人到文件", [this]() { exportContacts(); }));
    contactMenu->addItem(MenuItem("导入联系人", "从文件导入联系人", [this]() { importContacts(); }));
    contactMenu->addItem(MenuItem("返回用户菜单", "返回用户主菜单", [this]() { switchToMenu("user"); }));
}

void MenuSystem::initAdminMenu() {
    adminMenu = std::make_unique<Menu>("管理员功能");
    adminMenu->addItem(MenuItem("管理用户", "查看和管理用户账号", [this]() { adminManageUsers(); }));
    adminMenu->addItem(MenuItem("查看日志", "显示系统日志", [this]() { adminViewLogs(); }));
    adminMenu->addItem(MenuItem("返回", "返回用户菜单", [this]() { switchToMenu("user"); }));
}

void MenuSystem::initUserMenu() {
    userMenu = std::make_unique<Menu>("用户菜单");
    userMenu->addItem(MenuItem("联系人管理", "管理您的联系人", [this]() { switchToMenu("contact"); }));
    userMenu->addItem(MenuItem("修改密码", "更改您的账户密码", [this]() { changePassword(); }));
    userMenu->addItem(MenuItem("管理员功能", "访问管理员特殊功能（仅限管理员）", [this]() {
        auto user = userManager->getCurrentUser();
        if (user && user->getUserType() == "admin") {
            switchToMenu("admin");
        } else {
            std::string username = userManager->getCurrentUser()->getUsername();
            logger->warning("尝试访问管理员功能但无权限: " + username, username);
            std::cout << "您没有管理员权限！" << std::endl;
            pauseScreen();
        }
    }));
    userMenu->addItem(MenuItem("退出登录", "退出当前用户", [this]() { logout(); }));
}

void MenuSystem::run() {
    // 显示欢迎界面
    showStartupAnimation();

    bool running = true;

    while (running) {
        clearScreen();
        // 显示当前登录用户信息（如果已登录）
        if (userManager->isLoggedIn()) {
            auto        currentUser = userManager->getCurrentUser();
            std::string userInfo    = "当前用户: " + currentUser->getUsername() +
                                   " (" + currentUser->getUserType() + ")";
            std::cout << "\033[1;36m-----------------------------------------\033[0m" << std::endl;
            std::cout << "\033[1;96m" << userInfo << "\033[0m" << std::endl;
        }

        currentMenu->display();
        running = currentMenu->handleChoice();

        // 仅在不是主菜单时暂停，避免主菜单每次都要多按一次键
        if (running && currentMenu != mainMenu.get()) {
            pauseScreen();
        }
    }
}

// 简化的欢迎显示方法
void MenuSystem::showStartupAnimation() {
    clearScreen();
    std::cout << "\033[1;36m┌─────────────────────────────────────────┐\033[0m" << std::endl;
    std::cout << "\033[1;36m│\033[0m \033[1;97m       欢迎使用联系人管理系统 v1.0      \033[1;36m│\033[0m" << std::endl;
    std::cout << "\033[1;36m└─────────────────────────────────────────┘\033[0m" << std::endl;
    std::cout << std::endl;
    pauseScreen();
}

void MenuSystem::switchToMenu(const std::string& menuName) {
    if (menuName == "main") {
        currentMenu = mainMenu.get();
    } else if (menuName == "login") {
        currentMenu = loginMenu.get();
    } else if (menuName == "contact") {
        currentMenu = contactMenu.get();
    } else if (menuName == "admin") {
        currentMenu = adminMenu.get();
    } else if (menuName == "user") {
        currentMenu = userMenu.get();
    }
}

void MenuSystem::goBack() {
    // 默认返回主菜单，可以根据当前菜单决定返回哪个菜单
    if (currentMenu == contactMenu.get() || currentMenu == adminMenu.get()) {
        switchToMenu("user");
    } else {
        switchToMenu("main");
    }
}

void MenuSystem::login() {
    displayHeader("用户登录");

    std::string username;
    std::cout << "用户名: ";
    std::cin >> username;

    std::string password = getPasswordInput("密码: ");

    bool success = userManager->login(username, password);

    if (success) {
        auto user = userManager->getCurrentUser();
        logger->info("用户登录成功: " + username, username);
        std::cout << "登录成功！欢迎，" << username << std::endl;

        // 加载当前用户的联系人
        contactManager->setCurrentUser(user->getUserId());
        contactManager->loadContacts();

        switchToMenu("user");
    } else {
        logger->warning("用户登录失败: " + username, "System");
        std::cout << "登录失败！请检查用户名和密码。" << std::endl;
    }
}

void MenuSystem::registerUser() {
    displayHeader("用户注册");

    std::string username;
    std::cout << "请输入用户名（至少5个字符）: ";
    std::cin >> username;

    if (username.length() < 5) {
        logger->warning("注册失败：用户名太短", "System");
        std::cout << "用户名太短，必须至少5个字符！" << std::endl;
        return;
    }

    if (userManager->isUsernameExists(username)) {
        logger->warning("注册失败：用户名 " + username + " 已存在", "System");
        std::cout << "用户名已存在，请选择另一个用户名！" << std::endl;
        return;
    }

    std::string password = getPasswordInput("请输入密码（至少6个字符）: ");
    if (password.length() < 6) {
        logger->warning("密码太短，必须至少6个字符", "System");
        std::cout << "密码太短，必须至少6个字符！" << std::endl;
        return;
    }

    std::string confirmPassword = getPasswordInput("请确认密码: ");
    if (password != confirmPassword) {
        logger->warning("两次输入的密码不匹配", "System");
        std::cout << "两次输入的密码不匹配！" << std::endl;
        return;
    }

    bool success = userManager->registerUser(username, password);

    if (success) {
        logger->info("新用户注册: " + username, "System");
        std::cout << "注册成功！请登录。" << std::endl;
        switchToMenu("login");
    } else {
        logger->warning("用户注册失败: " + username, "System");
        std::cout << "注册失败！请稍后再试。" << std::endl;
        pauseScreen();
    }
}

void MenuSystem::logout() {
    std::string username = userManager->getCurrentUser() ? userManager->getCurrentUser()->getUsername() : "未知用户";

    // 先保存当前联系人数据（如果有修改）
    contactManager->saveContacts();

    userManager->logout();
    // 不再调用clear，只是重置联系人管理器状态
    contactManager->setCurrentUser(-1);

    logger->info("用户登出: " + username, username);
    std::cout << "您已成功退出登录。" << std::endl;
    switchToMenu("main");
}

void MenuSystem::viewContacts() {
    displayHeader("所有联系人");

    const auto& friends = contactManager->getAllFriends();

    if (friends.empty()) {
        std::cout << "您的通讯录中没有联系人。" << std::endl;
        return;
    }

    // 使用美化的表头替代原始的表头
    displayContactTableHeader();

    for (const auto& [phone, friend_] : friends) {
        std::cout << std::left
                  << std::setw(20) << friend_.getName()
                  << std::setw(15) << friend_.getPhoneNumber()
                  << std::setw(30) << friend_.getAddress()
                  << std::setw(15) << friend_.getBirthday()
                  << std::setw(25) << friend_.getEmail()
                  << friend_.getNote() << std::endl;
    }

    std::cout << std::endl
              << "总计 " << friends.size() << " 位联系人" << std::endl;
}

void MenuSystem::addContact() {
    displayHeader("添加联系人");

    std::string name, phone, address, birthday, email, note;

    std::cout << "姓名: ";
    std::getline(std::cin, name);

    std::cout << "电话号码: ";
    std::getline(std::cin, phone);

    // 检查电话号码是否已存在
    if (contactManager->getFriend(phone) != nullptr) {
        std::cout << "该电话号码已存在于通讯录中！" << std::endl;
        return;
    }

    std::cout << "地址: ";
    std::getline(std::cin, address);

    std::cout << "生日 (格式: YYYY-MM-DD): ";
    std::getline(std::cin, birthday);

    std::cout << "邮箱: ";
    std::getline(std::cin, email);

    std::cout << "备注: ";
    std::getline(std::cin, note);

    Friend newFriend(name, phone, address, birthday, email, note);

    bool success = contactManager->addFriend(newFriend);

    if (success) {
        contactManager->saveContacts();
        std::string username = userManager->getCurrentUser()->getUsername();
        std::cout << "联系人添加成功！" << std::endl;
    } else {
        std::string username = userManager->getCurrentUser()->getUsername();
        std::cout << "添加联系人失败，请检查输入信息。" << std::endl;
    }
}

void MenuSystem::deleteContact() {
    displayHeader("删除联系人");

    std::string phone;
    std::cout << "请输入要删除的联系人电话号码: ";
    std::getline(std::cin, phone);

    Friend* friend_ = contactManager->getFriend(phone);
    if (!friend_) {
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->warning("未找到联系人，电话号码: " + phone, username);
        std::cout << "未找到该电话号码的联系人！" << std::endl;
        return;
    }

    std::string name = friend_->getName();
    std::cout << "确定要删除联系人 " << name << " (" << phone << ") 吗? (y/n): ";
    char confirm;
    std::cin >> confirm;
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');

    if (confirm == 'y' || confirm == 'Y') {
        bool success = contactManager->deleteFriend(phone);

        if (success) {
            contactManager->saveContacts();
            std::string username = userManager->getCurrentUser()->getUsername();
            logger->info("删除了联系人: " + name + " (" + phone + ")", username);
            std::cout << "联系人已成功删除！" << std::endl;
        } else {
            std::string username = userManager->getCurrentUser()->getUsername();
            logger->warning("删除联系人失败: " + name + " (" + phone + ")", username);
            std::cout << "删除联系人失败！" << std::endl;
        }
    } else {
        std::cout << "操作已取消。" << std::endl;
    }
}

void MenuSystem::updateContact() {
    displayHeader("更新联系人");

    std::string phone;
    std::cout << "请输入要更新的联系人电话号码: ";
    std::getline(std::cin, phone);

    Friend* friend_ = contactManager->getFriend(phone);
    if (!friend_) {
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->warning("未找到联系人，电话号码: " + phone, username);
        std::cout << "未找到该电话号码的联系人！" << std::endl;
        return;
    }

    std::cout << "当前联系人信息:" << std::endl;
    std::cout << "姓名: " << friend_->getName() << std::endl;
    std::cout << "电话号码: " << friend_->getPhoneNumber() << std::endl;
    std::cout << "地址: " << friend_->getAddress() << std::endl;
    std::cout << "生日: " << friend_->getBirthday() << std::endl;
    std::cout << "邮箱: " << friend_->getEmail() << std::endl;
    std::cout << "备注: " << friend_->getNote() << std::endl;

    std::cout << std::endl
              << "请输入新的信息（直接按回车保持不变）:" << std::endl;

    std::string name, newPhone, address, birthday, email, note;
    std::string input;

    std::cout << "姓名: ";
    std::getline(std::cin, input);
    name = input.empty() ? friend_->getName() : input;

    std::cout << "电话号码: ";
    std::getline(std::cin, input);
    newPhone = input.empty() ? friend_->getPhoneNumber() : input;

    std::cout << "地址: ";
    std::getline(std::cin, input);
    address = input.empty() ? friend_->getAddress() : input;

    std::cout << "生日: ";
    std::getline(std::cin, input);
    birthday = input.empty() ? friend_->getBirthday() : input;

    std::cout << "邮箱: ";
    std::getline(std::cin, input);
    email = input.empty() ? friend_->getEmail() : input;

    std::cout << "备注: ";
    std::getline(std::cin, input);
    note = input.empty() ? friend_->getNote() : input;

    Friend updatedFriend(name, newPhone, address, birthday, email, note);

    bool success = contactManager->updateFriend(phone, updatedFriend);

    if (success) {
        contactManager->saveContacts();
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->info("更新了联系人: " + name + " (" + newPhone + ")", username);
        std::cout << "联系人已成功更新！" << std::endl;
    } else {
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->warning("更新联系人失败: " + name + " (" + newPhone + ")", username);
        std::cout << "更新联系人失败！" << std::endl;
    }
}

void MenuSystem::searchContact() {
    displayHeader("搜索联系人");

    std::cout << "1. 按姓名搜索" << std::endl;
    std::cout << "2. 按电话号码搜索" << std::endl;
    std::cout << "请选择搜索方式: ";

    int choice;
    std::cin >> choice;
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');

    if (choice == 1) {
        std::string name;
        std::cout << "请输入姓名关键字: ";
        std::getline(std::cin, name);

        auto results = contactManager->searchFriendsByName(name);

        if (results.empty()) {
            std::string username = userManager->getCurrentUser()->getUsername();
            logger->warning("搜索联系人失败，未找到匹配姓名: " + name, username);
            std::cout << "未找到匹配的联系人！" << std::endl;
            return;
        }

        std::cout << "找到 " << results.size() << " 个匹配的联系人:" << std::endl
                  << std::endl;

        std::cout << std::left
                  << std::setw(20) << "姓名"
                  << std::setw(15) << "电话"
                  << std::setw(30) << "地址"
                  << std::setw(15) << "生日"
                  << std::setw(25) << "邮箱"
                  << "备注" << std::endl;
        std::cout << std::string(110, '-') << std::endl;

        for (const auto& friend_ : results) {
            std::cout << std::left
                      << std::setw(20) << friend_.getName()
                      << std::setw(15) << friend_.getPhoneNumber()
                      << std::setw(30) << friend_.getAddress()
                      << std::setw(15) << friend_.getBirthday()
                      << std::setw(25) << friend_.getEmail()
                      << friend_.getNote() << std::endl;
        }
    } else if (choice == 2) {
        std::string phone;
        std::cout << "请输入电话号码: ";
        std::getline(std::cin, phone);

        Friend* friend_ = contactManager->getFriend(phone);

        if (!friend_) {
            std::string username = userManager->getCurrentUser()->getUsername();
            logger->warning("搜索联系人失败，未找到电话号码: " + phone, username);
            std::cout << "未找到该电话号码的联系人！" << std::endl;
            return;
        }

        std::cout << "找到联系人:" << std::endl
                  << std::endl;
        std::cout << "姓名: " << friend_->getName() << std::endl;
        std::cout << "电话号码: " << friend_->getPhoneNumber() << std::endl;
        std::cout << "地址: " << friend_->getAddress() << std::endl;
        std::cout << "生日: " << friend_->getBirthday() << std::endl;
        std::cout << "邮箱: " << friend_->getEmail() << std::endl;
        std::cout << "备注: " << friend_->getNote() << std::endl;
    } else {
        logger->warning("无效的选择", "System");
        std::cout << "无效的选择！" << std::endl;
    }
}

void MenuSystem::adminManageUsers() {
    auto currentUser = userManager->getCurrentUser();
    if (!currentUser || currentUser->getUserType() != "admin") {
        std::string username = currentUser ? currentUser->getUsername() : "未知用户";
        logger->warning("尝试访问管理员功能但无权限: " + username, username);
        std::cout << "您没有管理员权限！" << std::endl;
        return;
    }

    bool running = true;
    while (running) {
        displayHeader("用户管理");

        // 以管理员身份查看所有用户
        std::cout << "所有用户列表:" << std::endl;
        std::cout << std::left
                  << std::setw(5) << "ID"
                  << std::setw(20) << "用户名"
                  << std::setw(15) << "类型"
                  << std::setw(15) << "状态" << std::endl;
        std::cout << std::string(55, '-') << std::endl;

        // 直接从UserManager获取用户列表并显示
        logger->info("DatabaseManager: 正在加载所有用户数据");
        auto users = userManager->getAllUsers();

        for (const auto& userPtr : users) {
            if (userPtr) {
                std::cout << std::left
                          << std::setw(5) << userPtr->getUserId()
                          << std::setw(20) << userPtr->getUsername()
                          << std::setw(15) << userPtr->getUserType()
                          << std::setw(15) << (userPtr->getLoginStatus() ? "在线" : "离线")
                          << std::endl;
            }
        }
        std::cout << std::string(55, '-') << std::endl;

        logger->info("查询所有用户成功，共计 " + std::to_string(users.size()) + " 个用户。");

        std::cout << std::endl;
        std::cout << "1. 删除用户" << std::endl;
        std::cout << "2. 返回上级菜单" << std::endl;
        std::cout << "请选择操作: ";

        int choice;
        std::cin >> choice;
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');

        if (choice == 1) {
            std::string username;
            std::cout << "请输入要删除的用户名: ";
            std::getline(std::cin, username);

            if (username == currentUser->getUsername()) {
                logger->warning("管理员尝试删除自己的账户: " + username, currentUser->getUsername());
                std::cout << "您不能删除自己的账户！" << std::endl;
                continue;
            }

            std::cout << "确定要删除用户 " << username << " 吗? (y/n): ";
            char confirm;
            std::cin >> confirm;
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');

            if (confirm == 'y' || confirm == 'Y') {
                bool success = userManager->deleteUser(username);

                if (success) {
                    logger->info("删除了用户: " + username, currentUser->getUsername());
                    std::cout << "用户已成功删除！" << std::endl;
                } else {
                    logger->warning("删除用户失败: " + username, currentUser->getUsername());
                    std::cout << "删除用户失败！" << std::endl;
                }
            } else {
                std::cout << "操作已取消。" << std::endl;
            }
        } else if (choice == 2) {
            running = false;
        } else {
            std::cout << "无效的选择！" << std::endl;
        }

        if (running) {
            pauseScreen();
        }
    }
}

void MenuSystem::adminViewLogs() {
    auto currentUser = userManager->getCurrentUser();
    if (!currentUser || currentUser->getUserType() != "admin") {
        std::string username = currentUser ? currentUser->getUsername() : "未知用户";
        logger->warning("尝试访问管理员功能但无权限: " + username, username);
        std::cout << "您没有管理员权限！" << std::endl;
        return;
    }

    displayHeader("系统日志");

    auto adminUser = std::dynamic_pointer_cast<AdminUser>(currentUser);
    if (adminUser) {
        adminUser->viewLogs();
    } else {
        std::string username = currentUser ? currentUser->getUsername() : "未知用户";
        logger->warning("管理员无法访问日志功能: " + username, username);
        std::cout << "无法访问日志功能！" << std::endl;
    }
}

void MenuSystem::changePassword() {
    displayHeader("修改密码");

    auto currentUser = userManager->getCurrentUser();
    if (!currentUser) {
        std::cout << "您未登录！" << std::endl;
        return;
    }

    std::string oldPassword = getPasswordInput("请输入当前密码: ");

    // 验证当前密码
    if (!userManager->verifyPassword(currentUser->getUserId(), oldPassword)) {
        logger->warning("修改密码失败：当前密码不正确", currentUser->getUsername());
        std::cout << "当前密码不正确！" << std::endl;
        return;
    }

    std::string newPassword = getPasswordInput("请输入新密码（至少6个字符）: ");
    if (newPassword.length() < 6) {
        logger->warning("修改密码失败：新密码太短", currentUser->getUsername());
        std::cout << "密码太短，必须至少6个字符！" << std::endl;
        return;
    }

    std::string confirmPassword = getPasswordInput("请确认新密码: ");
    if (newPassword != confirmPassword) {
        logger->warning("修改密码失败：两次输入的密码不匹配", currentUser->getUsername());
        std::cout << "两次输入的密码不匹配！" << std::endl;
        return;
    }

    // 先保存当前通讯录以确保不丢失数据
    if (contactManager) {
        contactManager->saveContacts();
    }

    // 修改密码
    bool success = userManager->changePassword(currentUser->getUsername(), newPassword);

    if (success) {
        logger->info("用户修改了密码: " + currentUser->getUsername(), currentUser->getUsername());

        // 确保密码修改后通讯录状态保持一致
        // 注意：这里不需要重新加载联系人，因为我们已经保存并且用户ID没有变化
        // 只需要确保ContactManager知道当前的用户ID
        if (contactManager) {
            contactManager->setCurrentUser(currentUser->getUserId());
        }

        std::cout << "密码已成功修改！" << std::endl;
    } else {
        std::cout << "修改密码失败！" << std::endl;
    }
}

void MenuSystem::exportContacts() {
    displayHeader("导出联系人");

    std::string filename;
    std::cout << "请输入导出文件名: ";
    std::getline(std::cin, filename);

    if (filename.empty()) {
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->warning("导出联系人失败：文件名为空", username);
        std::cout << "文件名不能为空！" << std::endl;
        return;
    }

    // 确保文件名有适当的扩展名
    if (filename.find('.') == std::string::npos) {
        filename += ".csv";
    }

    std::ofstream outFile(filename);
    if (!outFile) {
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->warning("导出联系人失败：无法创建文件 " + filename, username);
        std::cout << "无法创建文件！" << std::endl;
        return;
    }

    // 写入CSV标题
    outFile << "姓名,电话号码,地址,生日,邮箱,备注\n";

    const auto& friends = contactManager->getAllFriends();
    for (const auto& [phone, friend_] : friends) {
        outFile << friend_.getName() << ","
                << friend_.getPhoneNumber() << ","
                << friend_.getAddress() << ","
                << friend_.getBirthday() << ","
                << friend_.getEmail() << ","
                << friend_.getNote() << "\n";
    }

    outFile.close();

    std::string username = userManager->getCurrentUser()->getUsername();
    logger->info("导出了联系人到文件: " + filename, username);
    std::cout << "成功导出 " << friends.size() << " 个联系人到 " << filename << std::endl;
}

void MenuSystem::importContacts() {
    displayHeader("导入联系人");

    std::string filename;
    std::cout << "请输入导入文件名 (CSV格式): ";
    std::getline(std::cin, filename);

    std::ifstream inFile(filename);
    if (!inFile) {
        std::string username = userManager->getCurrentUser()->getUsername();
        logger->warning("导入联系人失败：无法打开文件 " + filename, username);
        std::cout << "无法打开文件！" << std::endl;
        return;
    }

    std::string line;
    // 跳过标题行
    std::getline(inFile, line);

    int importCount = 0;
    int failCount   = 0;

    while (std::getline(inFile, line)) {
        std::stringstream ss(line);
        std::string       name, phone, address, birthday, email, note;

        // 解析CSV行
        std::getline(ss, name, ',');
        std::getline(ss, phone, ',');
        std::getline(ss, address, ',');
        std::getline(ss, birthday, ',');
        std::getline(ss, email, ',');
        std::getline(ss, note);

        Friend newFriend(name, phone, address, birthday, email, note);

        // 检查是否已存在该联系人
        if (contactManager->getFriend(phone) != nullptr) {
            failCount++;
            continue;
        }

        bool success = contactManager->addFriend(newFriend);
        if (success) {
            importCount++;
        } else {
            failCount++;
        }
    }

    inFile.close();

    if (importCount > 0) {
        contactManager->saveContacts();
    }

    std::string username = userManager->getCurrentUser()->getUsername();
    logger->info("从文件导入了 " + std::to_string(importCount) + " 个联系人: " + filename, username);
    std::cout << "导入完成！成功导入 " << importCount << " 个联系人，失败 " << failCount << " 个。" << std::endl;
}

void MenuSystem::showHelp() {
    displayHeader("帮助信息");

    std::cout << "联系人管理系统使用指南" << std::endl;
    std::cout << "======================" << std::endl
              << std::endl;

    std::cout << "1. 新用户请先注册账户。" << std::endl;
    std::cout << "2. 登录后可以管理您的联系人信息。" << std::endl;
    std::cout << "3. 您可以添加、删除、更新和搜索联系人。" << std::endl;
    std::cout << "4. 联系人信息包括姓名、电话号码、地址、生日、邮箱和备注。" << std::endl;
    std::cout << "5. 您可以导出联系人到CSV文件，也可以从CSV文件导入联系人。" << std::endl;
    std::cout << "6. 管理员用户可以查看所有用户和系统日志。" << std::endl
              << std::endl;

    std::cout << "技术支持：请联系系统管理员" << std::endl;
    pauseScreen();
}

void MenuSystem::exit() {
    displayHeader("退出系统");

    // 检查是否有未保存的修改
    if (userManager->isLoggedIn() && contactManager->hasModifications()) {
        std::cout << "您有未保存的联系人修改，确定要退出吗? (y/n): ";
        char confirm;
        std::cin >> confirm;

        if (confirm == 'y' || confirm == 'Y') {
            std::cout << "感谢使用联系人管理系统，再见！" << std::endl;
            ::exit(0);  // 使用标准库exit函数
        }
    } else {
        std::cout << "感谢使用联系人管理系统，再见！" << std::endl;
        ::exit(0);  // 使用标准库exit函数
    }
}
