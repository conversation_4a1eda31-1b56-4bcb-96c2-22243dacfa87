#include "../include/Friend.h"

using namespace std;

Friend::Friend() : name(""), phoneNumber(""), address(""), birthday(""), email(""), note("") {}
Friend::Friend(const std::string& name, const std::string& phoneNumber,
               const std::string& address, const std::string& birthday,
               const std::string& email, const std::string& note)
    : name(name), phoneNumber(phoneNumber), address(address), birthday(birthday), email(email), note(note) {}

// Getter方法
std::string Friend::getName() const {
    return this->name;
}
std::string Friend::getPhoneNumber() const {
    return this->phoneNumber;
}
std::string Friend::getAddress() const {
    return this->address;
}
std::string Friend::getBirthday() const {
    return this->birthday;
}
std::string Friend::getEmail() const {
    return this->email;
}
std::string Friend::getNote() const {
    return this->note;
}

// Setter方法
void Friend::setName(const std::string& name) {
    this->name = name;
}
void Friend::setPhoneNumber(const std::string& phoneNumber) {
    this->phoneNumber = phoneNumber;
}
void Friend::setAddress(const std::string& address) {
    this->address = address;
}
void Friend::setBirthday(const std::string& birthday) {
    this->birthday = birthday;
}
void Friend::setEmail(const std::string& email) {
    this->email = email;
}
void Friend::setNote(const std::string& note) {
    this->note = note;
}

// 打印好友信息
void Friend::display() const {
    cout << "姓名：" << name << endl;
    cout << "手机：" << phoneNumber << endl;
    cout << "地址：" << address << endl;
    cout << "生日：" << birthday << endl;
    cout << "邮箱：" << email << endl;
    cout << "备注：" << note << endl;
}

// 字符串化和有效性检查
std::string Friend::toString() const {
    return "姓名：" + name + "，手机：" + phoneNumber + "，地址：" + address + "，生日：" + birthday + "，邮箱：" + email + "，备注：" + note;
}

bool Friend::isValid() const {
    return !name.empty() && !phoneNumber.empty();
}